---
/**
 * Critical Request Chain Optimizer
 * Reduces critical request chains by implementing resource hints,
 * preloading critical assets, and optimizing loading sequence
 */

export interface Props {
  enableResourceHints?: boolean;
  enablePreloading?: boolean;
  enablePrefetching?: boolean;
  criticalAssets?: string[];
  preconnectDomains?: string[];
}

const {
  enableResourceHints = true,
  enablePreloading = true,
  enablePrefetching = true,
  criticalAssets = [
    '/logo.png'
  ],
  preconnectDomains = [
    'cdn.snipcart.com',
    'app.snipcart.com',
    'api.snipcart.com',
    'images.unsplash.com',
    'www.googletagmanager.com'
  ]
} = Astro.props;

// Critical resource loading sequence (disabled to prevent preload warnings)
const criticalSequence = [];

// Non-critical resources to prefetch (disabled to prevent 404 errors)
const prefetchResources = [];
---

<!-- Critical Resource Hints -->
{enableResourceHints && (
  <>
    <!-- Preconnect to critical domains (full connection setup) -->
    {preconnectDomains.map(domain => (
      <link rel="preconnect" href={`https://${domain}`} crossorigin />
    ))}
    
    <!-- DNS prefetch for less critical domains -->
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net" />
    <link rel="dns-prefetch" href="https://unpkg.com" />
  </>
)}

<!-- Preload Critical Assets -->
{enablePreloading && criticalSequence.map((asset, index) => (
  <link 
    rel="preload" 
    href={asset.url}
    as={asset.type}
    fetchpriority={asset.priority}
    crossorigin={asset.type === 'font' ? 'anonymous' : undefined}
  />
))}

<!-- Prefetch Non-Critical Resources -->
{enablePrefetching && prefetchResources.map(resource => (
  <link rel="prefetch" href={resource} />
))}

<!-- Critical Request Chain Optimization Script -->
<script is:inline>
  (function() {
    'use strict';
    
    // Critical Request Chain Optimizer
    const CriticalRequestOptimizer = {
      // Track request chains
      requestChains: new Map(),
      criticalResources: new Set(),
      loadingSequence: [],
      
      // Initialize critical resource tracking
      init: function() {
        this.trackCriticalResources();
        this.optimizeLoadingSequence();
        this.monitorRequestChains();
        this.implementResourcePrioritization();
      },
      
      // Track critical resources
      trackCriticalResources: function() {
        const criticalAssets = [
          '/logo.png'
        ];
        
        criticalAssets.forEach(asset => {
          this.criticalResources.add(asset);
        });
      },
      
      // Optimize loading sequence
      optimizeLoadingSequence: function() {
        // Ensure logo is loaded with high priority
        const logoImg = document.querySelector('img[src="/logo.png"]');
        if (logoImg) {
          logoImg.setAttribute('fetchpriority', 'high');
          logoImg.setAttribute('loading', 'eager');
        }
      },
      
      // Monitor request chains using Resource Timing API
      monitorRequestChains: function() {
        if ('PerformanceObserver' in window) {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            
            entries.forEach(entry => {
              this.analyzeRequestChain(entry);
            });
          });
          
          observer.observe({ entryTypes: ['resource'] });
        }
      },
      
      // Analyze individual request chains
      analyzeRequestChain: function(entry) {
        const url = entry.name;
        const isCritical = this.criticalResources.has(new URL(url).pathname);
        
        const chainInfo = {
          url: url,
          startTime: entry.startTime,
          duration: entry.duration,
          transferSize: entry.transferSize,
          isCritical: isCritical,
          isBlocking: this.isBlockingResource(url)
        };
        
        this.requestChains.set(url, chainInfo);
        
        // Log critical request chains that are too slow
        if (isCritical && entry.duration > 100) {
          console.warn('Slow critical resource detected:', {
            url: url,
            duration: entry.duration,
            transferSize: entry.transferSize
          });
        }
      },
      
      // Check if resource is render-blocking
      isBlockingResource: function(url) {
        const pathname = new URL(url).pathname;
        
        // CSS files are render-blocking
        if (pathname.endsWith('.css')) {
          return true;
        }
        
        // Synchronous JavaScript is render-blocking
        if (pathname.endsWith('.js')) {
          const script = document.querySelector(`script[src="${url}"]`);
          return script && !script.async && !script.defer;
        }
        
        return false;
      },
      
      // Implement resource prioritization
      implementResourcePrioritization: function() {
        // Set high priority for critical resources
        const criticalLinks = document.querySelectorAll('link[rel="stylesheet"], link[rel="preload"]');
        criticalLinks.forEach(link => {
          const href = link.getAttribute('href');
          if (href && this.criticalResources.has(href)) {
            link.setAttribute('fetchpriority', 'high');
          }
        });
        
        // Set low priority for non-critical resources
        const nonCriticalScripts = document.querySelectorAll('script[src]');
        nonCriticalScripts.forEach(script => {
          const src = script.getAttribute('src');
          if (src && !this.criticalResources.has(src)) {
            script.setAttribute('fetchpriority', 'low');
            if (!script.async && !script.defer) {
              script.defer = true;
            }
          }
        });
      },
      
      // Optimize third-party resources
      optimizeThirdPartyResources: function() {
        // Delay non-critical third-party scripts
        const thirdPartyScripts = [
          'https://www.googletagmanager.com/gtag/js',
          'https://cdn.snipcart.com/themes/v3.7.1/default/snipcart.js'
        ];
        
        thirdPartyScripts.forEach(scriptUrl => {
          const existingScript = document.querySelector(`script[src="${scriptUrl}"]`);
          if (existingScript) {
            existingScript.setAttribute('fetchpriority', 'low');
            existingScript.defer = true;
          }
        });
      },
      
      // Generate performance report
      generateReport: function() {
        const criticalChains = Array.from(this.requestChains.values())
          .filter(chain => chain.isCritical)
          .sort((a, b) => b.duration - a.duration);
        
        const blockingChains = Array.from(this.requestChains.values())
          .filter(chain => chain.isBlocking)
          .sort((a, b) => b.duration - a.duration);
        
        return {
          totalRequests: this.requestChains.size,
          criticalRequests: criticalChains.length,
          blockingRequests: blockingChains.length,
          slowestCritical: criticalChains[0],
          slowestBlocking: blockingChains[0],
          recommendations: this.generateRecommendations(criticalChains, blockingChains)
        };
      },
      
      // Generate optimization recommendations
      generateRecommendations: function(criticalChains, blockingChains) {
        const recommendations = [];
        
        if (criticalChains.length > 5) {
          recommendations.push('Consider reducing the number of critical resources');
        }
        
        if (blockingChains.some(chain => chain.duration > 200)) {
          recommendations.push('Some render-blocking resources are loading slowly');
        }
        
        if (blockingChains.length > 3) {
          recommendations.push('Consider making some resources non-blocking with async/defer');
        }
        
        return recommendations;
      }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        CriticalRequestOptimizer.init();
        CriticalRequestOptimizer.optimizeThirdPartyResources();
      });
    } else {
      CriticalRequestOptimizer.init();
      CriticalRequestOptimizer.optimizeThirdPartyResources();
    }
    
    // Generate report after page load
    window.addEventListener('load', () => {
      setTimeout(() => {
        const report = CriticalRequestOptimizer.generateReport();
        console.log('Critical Request Chain Analysis:', report);
      }, 2000);
    });
    
    // Expose for debugging
    window.CriticalRequestOptimizer = CriticalRequestOptimizer;
  })();
</script>

<!-- Critical CSS for request optimization -->
<style>
  /* Ensure critical resources load without layout shift */
  .critical-loading {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }
  
  .critical-loaded {
    opacity: 1;
  }
  
  /* Optimize for critical rendering path */
  .above-fold {
    contain: layout style paint;
  }
  
  /* Performance hints for browsers */
  .preload-hint {
    content-visibility: auto;
    contain-intrinsic-size: 300px;
  }
</style>
