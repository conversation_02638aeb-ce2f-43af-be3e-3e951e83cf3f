---
// Simple Snipcart cart button for header
---

<!-- Cart trigger button for header -->
<button 
  class="snipcart-checkout cart-trigger"
  type="button"
  aria-label="Open shopping cart"
>
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <circle cx="9" cy="21" r="1"></circle>
    <circle cx="20" cy="21" r="1"></circle>
    <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
  </svg>
  <span class="cart-count snipcart-items-count"></span>
</button>

<style>
  .cart-trigger {
    position: relative;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cart-trigger:hover {
    background-color: var(--bg-secondary);
    color: var(--primary);
  }

  .cart-trigger:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }

  .cart-count {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    line-height: 1;
  }

  .cart-count:empty {
    display: none;
  }

  /* Mobile adjustments */
  @media (max-width: 768px) {
    .cart-trigger {
      padding: 0.75rem;
    }
  }
</style>
