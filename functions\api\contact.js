/**
 * Cloudflare Pages Function for Contact Form Submissions
 * Sends <NAME_EMAIL>
 */

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Rate limiting - simple in-memory store (resets on function restart)
const rateLimitStore = new Map();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 3; // Max 3 submissions per 15 minutes per IP

// Validate form data
function validateFormData(formData) {
  const errors = [];
  
  const firstName = formData.get('firstName')?.trim();
  const lastName = formData.get('lastName')?.trim();
  const email = formData.get('email')?.trim();
  const subject = formData.get('subject')?.trim();
  const message = formData.get('message')?.trim();

  if (!firstName || firstName.length < 2) {
    errors.push('First name must be at least 2 characters long');
  }

  if (!lastName || lastName.length < 2) {
    errors.push('Last name must be at least 2 characters long');
  }

  if (!email || !EMAIL_REGEX.test(email)) {
    errors.push('Please provide a valid email address');
  }

  if (!subject) {
    errors.push('Please select a subject');
  }

  if (!message || message.length < 10) {
    errors.push('Message must be at least 10 characters long');
  }

  if (message && message.length > 2000) {
    errors.push('Message must be less than 2000 characters');
  }

  return {
    valid: errors.length === 0,
    errors,
    data: {
      firstName,
      lastName,
      email,
      phone: formData.get('phone')?.trim() || '',
      subject,
      message,
      newsletter: formData.get('newsletter') === 'yes'
    }
  };
}

// Check rate limiting
function checkRateLimit(ip) {
  const now = Date.now();
  const userRequests = rateLimitStore.get(ip) || [];
  
  // Remove old requests outside the window
  const recentRequests = userRequests.filter(timestamp => now - timestamp < RATE_LIMIT_WINDOW);
  
  if (recentRequests.length >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }
  
  // Add current request
  recentRequests.push(now);
  rateLimitStore.set(ip, recentRequests);
  
  return true;
}

// Format email content
function formatEmailContent(data) {
  const subjectMap = {
    'product-inquiry': 'Product Inquiry',
    'order-question': 'Order Question',
    'shipping': 'Shipping & Delivery',
    'returns': 'Returns & Exchanges',
    'general': 'General Question',
    'feedback': 'Feedback',
    'other': 'Other'
  };

  const subjectText = subjectMap[data.subject] || 'Contact Form Submission';
  
  return {
    subject: `[Cheers Marketplace] ${subjectText} from ${data.firstName} ${data.lastName}`,
    text: `
New contact form submission from Cheers Marketplace website:

Name: ${data.firstName} ${data.lastName}
Email: ${data.email}
Phone: ${data.phone || 'Not provided'}
Subject: ${subjectText}
Newsletter Signup: ${data.newsletter ? 'Yes' : 'No'}

Message:
${data.message}

---
Submitted at: ${new Date().toLocaleString('en-US', { timeZone: 'America/Los_Angeles' })} (Pacific Time)
IP Address: [Redacted for privacy]
    `.trim(),
    html: `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Contact Form Submission</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #92400e; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
    .content { background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0; border-top: none; border-radius: 0 0 8px 8px; }
    .field { margin-bottom: 15px; }
    .label { font-weight: bold; color: #475569; }
    .value { margin-top: 5px; }
    .message { background: white; padding: 15px; border-radius: 4px; border-left: 4px solid #92400e; }
    .footer { margin-top: 20px; font-size: 12px; color: #64748b; }
  </style>
</head>
<body>
  <div class="header">
    <h2>New Contact Form Submission</h2>
    <p>Cheers Marketplace Website</p>
  </div>
  
  <div class="content">
    <div class="field">
      <div class="label">Name:</div>
      <div class="value">${data.firstName} ${data.lastName}</div>
    </div>
    
    <div class="field">
      <div class="label">Email:</div>
      <div class="value"><a href="mailto:${data.email}">${data.email}</a></div>
    </div>
    
    <div class="field">
      <div class="label">Phone:</div>
      <div class="value">${data.phone || 'Not provided'}</div>
    </div>
    
    <div class="field">
      <div class="label">Subject:</div>
      <div class="value">${subjectText}</div>
    </div>
    
    <div class="field">
      <div class="label">Newsletter Signup:</div>
      <div class="value">${data.newsletter ? 'Yes' : 'No'}</div>
    </div>
    
    <div class="field">
      <div class="label">Message:</div>
      <div class="message">${data.message.replace(/\n/g, '<br>')}</div>
    </div>
    
    <div class="footer">
      Submitted at: ${new Date().toLocaleString('en-US', { timeZone: 'America/Los_Angeles' })} (Pacific Time)
    </div>
  </div>
</body>
</html>
    `.trim()
  };
}

// Send email using Cloudflare's Email Workers (if configured) or external service
async function sendEmail(emailContent, env) {
  // Option 1: Use Cloudflare Email Workers (requires setup)
  if (env.CLOUDFLARE_EMAIL_API_TOKEN && env.CLOUDFLARE_ZONE_ID) {
    try {
      const response = await fetch(`https://api.cloudflare.com/client/v4/zones/${env.CLOUDFLARE_ZONE_ID}/email/routing/rules`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${env.CLOUDFLARE_EMAIL_API_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: '<EMAIL>',
          from: '<EMAIL>',
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text
        })
      });

      if (response.ok) {
        return { success: true };
      }
    } catch (error) {
      console.error('Cloudflare email error:', error);
    }
  }

  // Option 2: Use Resend (recommended for simplicity)
  if (env.RESEND_API_KEY) {
    try {
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${env.RESEND_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          from: '<EMAIL>',
          to: '<EMAIL>',
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text
        })
      });

      if (response.ok) {
        return { success: true };
      }
    } catch (error) {
      console.error('Resend email error:', error);
    }
  }

  // Option 3: Use SendGrid
  if (env.SENDGRID_API_KEY) {
    try {
      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${env.SENDGRID_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          personalizations: [{
            to: [{ email: '<EMAIL>' }]
          }],
          from: { email: '<EMAIL>', name: 'Cheers Marketplace' },
          subject: emailContent.subject,
          content: [
            { type: 'text/html', value: emailContent.html },
            { type: 'text/plain', value: emailContent.text }
          ]
        })
      });

      if (response.ok) {
        return { success: true };
      }
    } catch (error) {
      console.error('SendGrid email error:', error);
    }
  }

  // If no email service is configured, log the message (for development)
  console.log('No email service configured. Message would be sent:', emailContent);
  
  return { 
    success: false, 
    error: 'Email service not configured. Please contact us <NAME_EMAIL>' 
  };
}

// Main handler
export async function onRequestPost(context) {
  const { request, env } = context;

  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('CF-Connecting-IP') || 
                    request.headers.get('X-Forwarded-For') || 
                    'unknown';

    // Check rate limiting
    if (!checkRateLimit(clientIP)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Too many requests. Please wait 15 minutes before submitting another message.'
        }),
        {
          status: 429,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Parse form data
    const formData = await request.formData();
    
    // Validate form data
    const validation = validateFormData(formData);
    if (!validation.valid) {
      return new Response(
        JSON.stringify({
          success: false,
          error: validation.errors.join(', ')
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Format email content
    const emailContent = formatEmailContent(validation.data);

    // Send email
    const emailResult = await sendEmail(emailContent, env);

    if (emailResult.success) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Your message has been sent successfully!'
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: emailResult.error || 'Failed to send email. Please try again or contact us directly.'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

  } catch (error) {
    console.error('Contact form error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: 'An unexpected error occurred. Please try again or contact us <NAME_EMAIL>.'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle GET requests (for testing)
export async function onRequestGet(context) {
  return new Response(
    JSON.stringify({
      success: false,
      error: 'This endpoint only accepts POST requests.',
      method: 'GET',
      timestamp: new Date().toISOString()
    }),
    {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}
