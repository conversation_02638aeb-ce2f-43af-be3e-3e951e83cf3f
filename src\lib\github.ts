// GitHub API integration for automatic commits
export interface GitHubConfig {
  owner: string;
  repo: string;
  token: string;
  branch?: string;
}

export interface CommitOptions {
  message: string;
  filePath: string;
  content: string;
  branch?: string;
}

export class GitHubService {
  private config: GitHubConfig;
  private baseUrl = 'https://api.github.com';

  constructor(config: GitHubConfig) {
    this.config = {
      ...config,
      branch: config.branch || 'main'
    };
  }

  /**
   * Get the current SHA of a file
   */
  private async getFileSha(filePath: string): Promise<string | null> {
    try {
      const response = await fetch(
        `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}/contents/${filePath}?ref=${this.config.branch}`,
        {
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Cheers-Marketplace-Admin'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        return data.sha;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting file SHA:', error);
      return null;
    }
  }

  /**
   * Commit and push a file to GitHub
   */
  async commitFile(options: CommitOptions): Promise<{ success: boolean; error?: string; commitSha?: string }> {
    console.log('=== GITHUB SERVICE: commitFile ===');
    console.log('Commit options:', {
      message: options.message,
      filePath: options.filePath,
      contentLength: options.content.length,
      branch: options.branch || this.config.branch
    });

    try {
      const { message, filePath, content, branch = this.config.branch } = options;

      console.log('=== GETTING CURRENT FILE SHA ===');
      // Get current file SHA (needed for updates)
      const currentSha = await this.getFileSha(filePath);
      console.log('Current file SHA:', currentSha || 'null (new file)');

      console.log('=== PREPARING COMMIT DATA ===');
      // Prepare the commit data
      // Use btoa instead of Buffer for Cloudflare Pages compatibility
      const commitData: any = {
        message,
        content: btoa(content), // btoa is available in Cloudflare Workers/Pages
        branch
      };

      // Include SHA if file exists (for updates)
      if (currentSha) {
        commitData.sha = currentSha;
        console.log('Added SHA to commit data for file update');
      } else {
        console.log('No SHA needed - creating new file');
      }

      console.log('Commit data prepared:', {
        message: commitData.message,
        branch: commitData.branch,
        hasSha: !!commitData.sha,
        contentLength: commitData.content.length
      });

      console.log('=== MAKING GITHUB API REQUEST ===');
      const apiUrl = `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}/contents/${filePath}`;
      console.log('API URL:', apiUrl);

      // Make the commit
      const response = await fetch(apiUrl, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json',
          'User-Agent': 'Cheers-Marketplace-Admin'
        },
        body: JSON.stringify(commitData)
      });

      console.log('=== GITHUB API RESPONSE ===');
      console.log('GitHub API response status:', response.status);
      console.log('GitHub API response statusText:', response.statusText);
      console.log('GitHub API response ok:', response.ok);

      if (response.ok) {
        console.log('✅ GitHub API request successful');
        const result = await response.json();
        console.log('GitHub API response data:', JSON.stringify(result, null, 2));
        console.log('Commit SHA:', result.commit?.sha);

        return {
          success: true,
          commitSha: result.commit?.sha
        };
      } else {
        console.log('❌ GitHub API request failed');
        let errorData;
        try {
          errorData = await response.json();
          console.log('GitHub API error data:', JSON.stringify(errorData, null, 2));
        } catch (e) {
          console.log('Could not parse error response as JSON');
          errorData = {};
        }

        const errorMessage = `GitHub API error: ${response.status} - ${errorData.message || response.statusText}`;
        console.error('Final error message:', errorMessage);

        return {
          success: false,
          error: errorMessage
        };
      }
    } catch (error) {
      console.error('❌ GitHub commit error (exception):', error);
      const errorMessage = `Failed to commit to GitHub: ${error instanceof Error ? error.message : String(error)}`;
      console.error('Final error message:', errorMessage);

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Commit products.json file specifically
   */
  async commitProducts(products: any[]): Promise<{ success: boolean; error?: string; commitSha?: string }> {
    console.log('=== GITHUB SERVICE: commitProducts ===');
    console.log('Products to commit:', products.length);
    console.log('Product IDs:', products.map((p: any) => p.id));

    const content = JSON.stringify(products, null, 2);
    const timestamp = new Date().toISOString();

    console.log('Generated content length:', content.length);
    console.log('Content preview (first 500 chars):', content.substring(0, 500));

    const commitOptions = {
      message: `Update products.json - ${products.length} products (${timestamp})`,
      filePath: 'src/data/products.json',
      content
    };

    console.log('Commit options:', commitOptions.message, commitOptions.filePath);

    const result = await this.commitFile(commitOptions);
    console.log('commitFile result:', JSON.stringify(result, null, 2));

    return result;
  }

  /**
   * Commit both products and categories
   */
  async commitProductsAndCategories(products: any[], categories: string[]): Promise<{ success: boolean; error?: string; commitSha?: string }> {
    console.log('=== GITHUB SERVICE: commitProductsAndCategories ===');
    console.log('Products to commit:', products.length);
    console.log('Categories to commit:', categories.length);
    console.log('Categories:', categories);

    try {
      // Prepare products content
      const productsContent = JSON.stringify(products, null, 2);
      const categoriesContent = JSON.stringify(categories, null, 2);
      const timestamp = new Date().toISOString();

      // Get latest commit SHA first to get the base tree
      const refResponse = await fetch(
        `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}/git/ref/heads/${this.config.branch}`,
        {
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Cheers-Marketplace-Admin'
          }
        }
      );

      if (!refResponse.ok) {
        throw new Error('Failed to get latest commit');
      }

      const refData = await refResponse.json();
      const latestCommitSha = refData.object.sha;

      // Prepare tree for multiple file commit - ONLY update specific files, preserve all others
      const tree = [
        {
          path: 'src/data/products.json',
          mode: '100644',
          type: 'blob',
          content: productsContent
        },
        {
          path: 'src/data/categories.json',
          mode: '100644',
          type: 'blob',
          content: categoriesContent
        }
      ];

      // Create tree based on latest commit (preserves all existing files)
      const treeResponse = await fetch(
        `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}/git/trees`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Cheers-Marketplace-Admin',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            base_tree: latestCommitSha,
            tree
          })
        }
      );

      if (!treeResponse.ok) {
        const error = await treeResponse.text();
        throw new Error(`Failed to create tree: ${error}`);
      }

      const treeData = await treeResponse.json();

      // Create commit
      const commitResponse = await fetch(
        `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}/git/commits`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Cheers-Marketplace-Admin',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            message: `Update products and categories - ${products.length} products, ${categories.length} categories (${timestamp})`,
            tree: treeData.sha,
            parents: [latestCommitSha]
          })
        }
      );

      if (!commitResponse.ok) {
        const error = await commitResponse.text();
        throw new Error(`Failed to create commit: ${error}`);
      }

      const commitData = await commitResponse.json();

      // Update reference
      const updateRefResponse = await fetch(
        `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}/git/refs/heads/${this.config.branch}`,
        {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Cheers-Marketplace-Admin',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            sha: commitData.sha
          })
        }
      );

      if (!updateRefResponse.ok) {
        const error = await updateRefResponse.text();
        throw new Error(`Failed to update reference: ${error}`);
      }

      console.log('✅ Successfully committed products and categories');
      return { success: true, commitSha: commitData.sha };

    } catch (error) {
      console.error('❌ Error committing products and categories:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Test GitHub connection
   */
  async testConnection(): Promise<{ success: boolean; error?: string; repoInfo?: any }> {
    try {
      const response = await fetch(
        `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}`,
        {
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Cheers-Marketplace-Admin'
          }
        }
      );

      if (response.ok) {
        const repoInfo = await response.json();
        return {
          success: true,
          repoInfo: {
            name: repoInfo.name,
            fullName: repoInfo.full_name,
            defaultBranch: repoInfo.default_branch,
            private: repoInfo.private
          }
        };
      } else {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: `GitHub API error: ${response.status} - ${errorData.message || response.statusText}`
        };
      }
    } catch (error) {
      console.error('GitHub connection test error:', error);
      return {
        success: false,
        error: `Failed to connect to GitHub: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
}

/**
 * Create GitHub service instance from environment variables
 * In Cloudflare Pages, environment variables are passed via the context
 */
export function createGitHubService(env?: any): GitHubService | null {
  // Try multiple ways to access environment variables
  const owner = env?.GITHUB_OWNER || import.meta.env.GITHUB_OWNER || process.env.GITHUB_OWNER;
  const repo = env?.GITHUB_REPO || import.meta.env.GITHUB_REPO || process.env.GITHUB_REPO;
  const token = env?.GITHUB_TOKEN || import.meta.env.GITHUB_TOKEN || process.env.GITHUB_TOKEN;
  const branch = env?.GITHUB_BRANCH || import.meta.env.GITHUB_BRANCH || process.env.GITHUB_BRANCH || 'main';

  console.log('GitHub config check:', {
    owner: owner ? '***' : 'missing',
    repo: repo ? '***' : 'missing',
    token: token ? '***' : 'missing',
    branch: branch || 'main'
  });

  if (!owner || !repo || !token) {
    console.warn('GitHub configuration missing. Required: GITHUB_OWNER, GITHUB_REPO, GITHUB_TOKEN');
    console.warn('Available env keys:', env ? Object.keys(env) : 'no env object');
    return null;
  }

  return new GitHubService({ owner, repo, token, branch });
}
