---
// Product Card Component - Refactored with smaller components
import ProductImage from './product/ProductImage.astro';
import ProductInfo from './product/ProductInfo.astro';

interface Props {
  product: {
    id: string;
    name: string;
    slug: string;
    category: string;
    condition: string;
    price: number;
    displayPrice: string;
    description: string;
    shortDescription: string;
    firstImage?: string;
    imageCount: number;
    hasMultipleImages: boolean;
    hasDefects: boolean;
    keyPoints: Array<{label: string; value: string}>;
    index: number;
  };
}

const { product } = Astro.props;
---

<article 
  class="product-card" 
  data-category={product.category} 
  data-name={product.name} 
  data-description={product.shortDescription}
  data-price={product.price}
  data-index={product.index}
>
  <a href={`/products/${product.slug}/`} class="product-link" aria-label={`View ${product.name} - ${product.displayPrice}`}>
    <ProductImage
      name={product.name}
      firstImage={product.firstImage}
      imageCount={product.imageCount}
      hasMultipleImages={product.hasMultipleImages}
      hasDefects={product.hasDefects}
      index={product.index}
      slug={product.slug}
    />

    <ProductInfo
      name={product.name}
      category={product.category}
      condition={product.condition}
      shortDescription={product.shortDescription}
      description={product.description}
      displayPrice={product.displayPrice}
      keyPoints={product.keyPoints}
    />
  </a>
</article>

<style>
  .product-card {
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
  }

  .product-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }

  .product-link {
    display: flex;
    flex-direction: column;
    text-decoration: none;
    color: inherit;
  }

  .product-link:hover {
    text-decoration: none;
  }

  /* Focus styles for accessibility */
  .product-link:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .product-card:hover {
      transform: none;
    }
  }
</style>
