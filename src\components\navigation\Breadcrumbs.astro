---
// Breadcrumb Navigation Component
// Provides navigation breadcrumbs with structured data for SEO

import BreadcrumbSchema from '../seo/BreadcrumbSchema.astro';
import type { BreadcrumbItem } from '../../utils/structuredData';

export interface Props {
  items: BreadcrumbItem[];
  showHome?: boolean;
  separator?: string;
  class?: string;
  ariaLabel?: string;
}

const {
  items,
  showHome = true,
  separator = '›',
  class: className = '',
  ariaLabel = 'Breadcrumb navigation'
} = Astro.props;

// Ensure home is included if showHome is true
let breadcrumbItems = [...items];

if (showHome && (!breadcrumbItems.length || breadcrumbItems[0].name !== 'Home')) {
  breadcrumbItems.unshift({
    name: 'Home',
    url: '/',
    position: 1
  });
  
  // Reposition other items
  breadcrumbItems = breadcrumbItems.map((item, index) => ({
    ...item,
    position: index + 1
  }));
}

// Filter out invalid items and ensure we have at least 2 items for breadcrumbs
const validItems = breadcrumbItems.filter(item => item.name && item.url);
const showBreadcrumbs = validItems.length > 1;
---

{showBreadcrumbs && (
  <>
    <!-- Structured Data for SEO -->
    <BreadcrumbSchema items={validItems} />
    
    <!-- Visual Breadcrumb Navigation -->
    <nav 
      class={`breadcrumbs ${className}`}
      aria-label={ariaLabel}
      role="navigation"
    >
      <ol class="breadcrumb-list" itemscope itemtype="https://schema.org/BreadcrumbList">
        {validItems.map((item, index) => {
          const isLast = index === validItems.length - 1;
          const isFirst = index === 0;
          
          return (
            <li 
              class={`breadcrumb-item ${isLast ? 'current' : ''} ${isFirst ? 'home' : ''}`}
              itemprop="itemListElement" 
              itemscope 
              itemtype="https://schema.org/ListItem"
            >
              {!isLast ? (
                <a 
                  href={item.url}
                  class="breadcrumb-link"
                  itemprop="item"
                  aria-label={`Go to ${item.name}`}
                >
                  <span itemprop="name">{item.name}</span>
                </a>
              ) : (
                <span 
                  class="breadcrumb-current"
                  itemprop="name"
                  aria-current="page"
                >
                  {item.name}
                </span>
              )}
              
              <meta itemprop="position" content={item.position.toString()} />
              
              {!isLast && (
                <span class="breadcrumb-separator" aria-hidden="true">
                  {separator}
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  </>
)}

<style>
  .breadcrumbs {
    margin: 0;
    padding: 0;
    background: none;
    border: none;
  }

  .breadcrumb-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.375rem;
    margin: 0;
    padding: 0;
    list-style: none;
    font-size: 0.8125rem;
    line-height: 1.4;
    color: #6b7280;
  }

  .breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 0.375rem;
  }

  .breadcrumb-link {
    color: #6b7280;
    text-decoration: none;
    transition: color 0.2s ease;
    font-weight: 400;
    padding: 0;
  }

  .breadcrumb-link:hover {
    color: var(--primary);
    text-decoration: none;
  }

  .breadcrumb-link:focus {
    outline: 1px solid var(--primary);
    outline-offset: 2px;
    border-radius: 2px;
  }

  .breadcrumb-current {
    color: #374151;
    font-weight: 500;
    padding: 0;
  }

  .breadcrumb-separator {
    color: #d1d5db;
    font-weight: 400;
    user-select: none;
    font-size: 0.75rem;
  }

  /* Remove home icon for cleaner look */
  .breadcrumb-item.home .breadcrumb-link::before {
    display: none;
  }

  /* Current page styling */
  .breadcrumb-item.current {
    color: #374151;
  }
  
  /* Responsive design */
  @media (max-width: 640px) {
    .breadcrumb-list {
      font-size: 0.75rem;
      gap: 0.25rem;
    }

    /* Hide intermediate breadcrumbs on very small screens for cleaner look */
    .breadcrumb-item:not(.home):not(.current):not(:nth-last-child(2)) {
      display: none;
    }

    /* Add ellipsis when items are hidden */
    .breadcrumb-item.home + .breadcrumb-item:not(.current)::before {
      content: '...';
      color: #d1d5db;
      margin-right: 0.25rem;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .breadcrumb-link {
      color: #000;
    }

    .breadcrumb-current {
      color: #000;
      font-weight: 600;
    }

    .breadcrumb-separator {
      color: #000;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .breadcrumb-link {
      transition: none;
    }
  }

  /* Print styles */
  @media print {
    .breadcrumb-list {
      color: #000;
    }

    .breadcrumb-link {
      color: #000;
    }

    .breadcrumb-separator {
      color: #000;
    }
  }
</style>

<!-- 
Usage Examples:

1. Basic breadcrumbs:
<Breadcrumbs items={[
  { name: "Products", url: "/products", position: 2 },
  { name: "Electronics", url: "/products?category=electronics", position: 3 },
  { name: "Smartphone", url: "/products/smartphone", position: 4 }
]} />

2. Custom separator:
<Breadcrumbs 
  items={breadcrumbItems}
  separator=">"
  class="custom-breadcrumbs"
/>

3. Without home:
<Breadcrumbs 
  items={breadcrumbItems}
  showHome={false}
/>

4. Custom aria label:
<Breadcrumbs 
  items={breadcrumbItems}
  ariaLabel="Page navigation breadcrumbs"
/>
-->
