---
// Compact Integrated Admin Header - All-in-one header with navigation and stats
---

<div class="compact-admin-header">
  <!-- Top Row: Title + Actions -->
  <div class="header-top-row">
    <div class="admin-title-compact">
      <h1>Product Management</h1>
      <span class="subtitle">Manage your catalog</span>
    </div>
    <div class="admin-actions-compact">
      <div class="github-status" id="github-status">
        <span class="status-indicator" id="github-indicator">⚪</span>
        <span class="status-text" id="github-text">Connected</span>
      </div>
      <button id="sync-products" class="btn-sync-compact" title="Sync with server and deploy">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12,18A6,6 0 0,1 6,12C6,11 6.25,10.03 6.7,9.2L5.24,7.74C4.46,8.97 4,10.43 4,12A8,8 0 0,0 12,20V23L16,19L12,15M12,4V1L8,5L12,9V6A6,6 0 0,1 18,12C18,13 17.75,13.97 17.3,14.8L18.76,16.26C19.54,15.03 20,13.57 20,12A8,8 0 0,0 12,4Z"/>
        </svg>
        Sync & Deploy
      </button>
      <button id="test-github" class="btn-secondary-compact" title="Test GitHub configuration">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12,2A10,10 0 0,0 2,12C2,16.42 4.87,20.17 8.84,21.5C9.34,21.58 9.5,21.27 9.5,21C9.5,20.77 9.5,20.14 9.5,19.31C6.73,19.91 6.14,17.97 6.14,17.97C5.68,16.81 5.03,16.5 5.03,16.5C4.12,15.88 5.1,15.9 5.1,15.9C6.1,15.97 6.63,16.93 6.63,16.93C7.5,18.45 8.97,18 9.54,17.76C9.63,17.11 9.89,16.67 10.17,16.42C7.95,16.17 5.62,15.31 5.62,11.5C5.62,10.39 6,9.5 6.65,8.79C6.55,8.54 6.2,7.5 6.75,6.15C6.75,6.15 7.59,5.88 9.5,7.17C10.29,6.95 11.15,6.84 12,6.84C12.85,6.84 13.71,6.95 14.5,7.17C16.41,5.88 17.25,6.15 17.25,6.15C17.8,7.5 17.45,8.54 17.35,8.79C18,9.5 18.38,10.39 18.38,11.5C18.38,15.32 16.04,16.16 13.81,16.41C14.17,16.72 14.5,17.33 14.5,18.26C14.5,19.6 14.5,20.68 14.5,21C14.5,21.27 14.66,21.59 15.17,21.5C19.14,20.16 22,16.42 22,12A10,10 0 0,0 12,2Z"/>
        </svg>
        Test GitHub
      </button>
      <button id="add-product-btn" class="btn-primary-compact">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
        </svg>
        Add Product
      </button>
    </div>
  </div>

  <!-- Bottom Row: Navigation + Stats + Filters -->
  <div class="header-bottom-row">
    <!-- Navigation Tabs -->
    <div class="admin-nav-compact">
      <button id="tab-list" class="admin-tab-btn admin-tab-btn-compact active" data-tab="list">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8V5H4M4,19H8V15H4M4,14H8V10H4V14Z"/>
        </svg>
        Product List
      </button>
      <button id="tab-form" class="admin-tab-btn admin-tab-btn-compact" data-tab="form">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
        </svg>
        Add/Edit Product
      </button>
      <button id="tab-categories" class="admin-tab-btn admin-tab-btn-compact" data-tab="categories">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
          <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z"/>
        </svg>
        Categories
      </button>
    </div>

    <!-- Inline Stats -->
    <div class="admin-stats-inline">
      <div class="stat-item-inline">
        <span class="stat-number-inline" id="total-products">0</span>
        <span class="stat-label-inline">Total</span>
      </div>
      <div class="stat-item-inline">
        <span class="stat-number-inline" id="visible-products">0</span>
        <span class="stat-label-inline">Visible</span>
      </div>
      <div class="stat-item-inline">
        <span class="stat-number-inline" id="categories-count">0</span>
        <span class="stat-label-inline">Categories</span>
      </div>
    </div>

    <!-- Compact Filters -->
    <div class="admin-filters-compact">
      <select id="admin-category-filter" class="admin-select-compact">
        <option value="">All Categories</option>
        <option value="Clothing">Clothing</option>
        <option value="Books">Books</option>
        <option value="Arts & Crafts">Arts & Crafts</option>
        <option value="Home Decor">Home Decor</option>
        <option value="Electronics">Electronics</option>
        <option value="Toys & Games">Toys & Games</option>
      </select>
      <select id="admin-sort-by" class="admin-select-compact">
        <option value="name">Name</option>
        <option value="price">Price</option>
        <option value="category">Category</option>
        <option value="newest">Newest</option>
      </select>
      <div class="search-input-wrapper-compact">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
        </svg>
        <input
          id="admin-search"
          type="search"
          placeholder="Search products..."
          class="admin-input-compact"
        />
      </div>
    </div>
  </div>
</div>
