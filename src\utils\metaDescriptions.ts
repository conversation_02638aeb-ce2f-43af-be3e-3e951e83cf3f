/**
 * Meta Description Generation Utilities
 * Creates SEO-optimized meta descriptions for different page types
 */

export interface Product {
  name: string;
  category: string;
  price: number;
  shortDescription?: string;
  description?: string;
  condition?: string;
  brand?: string;
}

export interface MetaDescriptionConfig {
  siteName?: string;
  location?: string;
  maxLength?: number;
  includePrice?: boolean;
  includeBrand?: boolean;
  includeCondition?: boolean;
}

const DEFAULT_CONFIG: Required<MetaDescriptionConfig> = {
  siteName: 'Cheers Marketplace',
  location: 'Chico, CA',
  maxLength: 160,
  includePrice: true,
  includeBrand: true,
  includeCondition: true
};

/**
 * Generate homepage meta description
 */
export function generateHomepageDescription(config: MetaDescriptionConfig = {}): string {
  const { siteName, location } = { ...DEFAULT_CONFIG, ...config };
  
  return `Discover unique, curated products from passionate creators at ${siteName}. Quality secondhand goods, vintage finds, and local treasures in ${location}. Shop sustainable, save money.`;
}

/**
 * Generate product page meta description
 */
export function generateProductDescription(
  product: Product,
  config: MetaDescriptionConfig = {}
): string {
  const {
    siteName,
    location,
    maxLength,
    includePrice,
    includeBrand,
    includeCondition
  } = { ...DEFAULT_CONFIG, ...config };

  let description = '';
  
  // Start with product name and category
  description += `${product.name} - ${product.category}`;
  
  // Add brand if available and enabled
  if (includeBrand && product.brand) {
    description += ` by ${product.brand}`;
  }
  
  // Add condition if available and enabled
  if (includeCondition && product.condition) {
    const conditionText = product.condition.toLowerCase().includes('new') ? 'new' : 'pre-owned';
    description += ` (${conditionText})`;
  }
  
  // Add price if enabled
  if (includePrice) {
    description += ` for $${product.price.toFixed(2)}`;
  }
  
  // Add short description or truncated description
  const productDesc = product.shortDescription || product.description;
  if (productDesc) {
    // Less aggressive cleaning, allows for more characters like apostrophes.
    const cleanDesc = productDesc.replace(/[\r\n\t]+/g, ' ').replace(/\s+/g, ' ').trim();
    description += `. ${cleanDesc}`;
  }
  
  // Add location and site name
  description += ` Available at ${siteName} in ${location}.`;
  
  // Add call to action
  description += ' Shop quality secondhand goods today!';
  
  // Truncate if too long
  if (description.length > maxLength) {
    let truncated = description.substring(0, maxLength - 3);
    // Truncate at the last space to avoid cutting words in half
    if (truncated.lastIndexOf(' ') > 0) {
      truncated = truncated.substring(0, truncated.lastIndexOf(' '));
    }
    description = truncated + '...';
  }
  
  return description;
}

/**
 * Generate products listing page meta description
 */
export function generateProductsListingDescription(
  category?: string,
  searchQuery?: string,
  totalProducts?: number,
  config: MetaDescriptionConfig = {}
): string {
  const { siteName, location } = { ...DEFAULT_CONFIG, ...config };
  
  let description = '';
  
  if (searchQuery) {
    description = `Search results for "${searchQuery}" at ${siteName}`;
    if (totalProducts !== undefined) {
      description += ` - ${totalProducts} products found`;
    }
    description += `. Quality secondhand goods in ${location}.`;
  } else if (category) {
    const categoryName = formatCategoryName(category);
    description = `Shop ${categoryName} at ${siteName} in ${location}`;
    if (totalProducts !== undefined) {
      description += ` - ${totalProducts} quality items available`;
    }
    description += '. Curated secondhand goods from local sellers.';
  } else {
    description = `Browse all products at ${siteName} in ${location}`;
    if (totalProducts !== undefined) {
      description += ` - ${totalProducts} unique items`;
    }
    description += '. Quality secondhand goods, vintage finds, and local treasures.';
  }
  
  return description;
}

/**
 * Generate about page meta description
 */
export function generateAboutDescription(config: MetaDescriptionConfig = {}): string {
  const { siteName, location } = { ...DEFAULT_CONFIG, ...config };
  
  return `Learn about ${siteName}, a family-run business in ${location} specializing in quality secondhand goods. We personally inspect every item to ensure you get the best value and quality.`;
}

/**
 * Generate FAQ page meta description
 */
export function generateFAQDescription(config: MetaDescriptionConfig = {}): string {
  const { siteName } = { ...DEFAULT_CONFIG, ...config };
  
  return `Frequently asked questions about shopping at ${siteName}. Learn about our quality standards, shipping, returns, and how we ensure every item meets our high standards.`;
}

/**
 * Generate terms page meta description
 */
export function generateTermsDescription(config: MetaDescriptionConfig = {}): string {
  const { siteName } = { ...DEFAULT_CONFIG, ...config };
  
  return `Terms and conditions for shopping at ${siteName}. Read our policies on purchases, returns, shipping, and user responsibilities for a safe shopping experience.`;
}

/**
 * Generate privacy policy meta description
 */
export function generatePrivacyDescription(config: MetaDescriptionConfig = {}): string {
  const { siteName } = { ...DEFAULT_CONFIG, ...config };
  
  return `Privacy policy for ${siteName}. Learn how we protect your personal information, use cookies, and handle your data when you shop with us.`;
}

/**
 * Generate category-specific meta description
 */
export function generateCategoryDescription(
  category: string,
  productCount?: number,
  config: MetaDescriptionConfig = {}
): string {
  const { siteName, location } = { ...DEFAULT_CONFIG, ...config };
  
  const categoryName = formatCategoryName(category);
  const categoryDescriptions: Record<string, string> = {
    'electronics': 'smartphones, laptops, tablets, and tech accessories',
    'clothing': 'vintage fashion, designer pieces, and everyday wear',
    'home-garden': 'furniture, decor, tools, and garden supplies',
    'books': 'novels, textbooks, rare books, and educational materials',
    'toys-games': 'vintage toys, board games, and educational toys',
    'sports-outdoors': 'equipment, gear, and outdoor adventure items',
    'health-beauty': 'skincare, wellness products, and beauty items',
    'automotive': 'car parts, accessories, and automotive tools'
  };
  
  const categoryDesc = categoryDescriptions[category] || 'quality secondhand items';
  
  let description = `Shop ${categoryName} at ${siteName} in ${location} - ${categoryDesc}`;
  
  if (productCount !== undefined) {
    description += `. ${productCount} curated items available`;
  }
  
  description += '. Quality guaranteed, personally inspected, great prices.';
  
  return description;
}

/**
 * Generate search results meta description
 */
export function generateSearchDescription(
  query: string,
  resultCount?: number,
  config: MetaDescriptionConfig = {}
): string {
  const { siteName, location } = { ...DEFAULT_CONFIG, ...config };
  
  let description = `Search results for "${query}" at ${siteName} in ${location}`;
  
  if (resultCount !== undefined) {
    if (resultCount === 0) {
      description += '. No results found, but browse our other quality secondhand goods.';
    } else {
      description += `. ${resultCount} quality items found. Curated secondhand goods with guaranteed quality.`;
    }
  } else {
    description += '. Find quality secondhand goods, vintage treasures, and unique items.';
  }
  
  return description;
}

/**
 * Format category name for display
 */
function formatCategoryName(category: string): string {
  const categoryNames: Record<string, string> = {
    'electronics': 'Electronics',
    'clothing': 'Clothing & Fashion',
    'home-garden': 'Home & Garden',
    'books': 'Books',
    'toys-games': 'Toys & Games',
    'sports-outdoors': 'Sports & Outdoors',
    'health-beauty': 'Health & Beauty',
    'automotive': 'Automotive'
  };
  
  return categoryNames[category] || category
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Validate meta description length
 */
export function validateMetaDescription(description: string, maxLength: number = 160): {
  isValid: boolean;
  length: number;
  recommendation: string;
} {
  const length = description.length;
  
  if (length <= maxLength) {
    return {
      isValid: true,
      length,
      recommendation: 'Meta description length is optimal.'
    };
  }
  
  return {
    isValid: false,
    length,
    recommendation: `Meta description is ${length - maxLength} characters too long. Consider shortening it.`
  };
}

/**
 * Generate fallback meta description
 */
export function generateFallbackDescription(
  pageTitle: string,
  config: MetaDescriptionConfig = {}
): string {
  const { siteName, location } = { ...DEFAULT_CONFIG, ...config };
  
  return `${pageTitle} - ${siteName} in ${location}. Quality secondhand goods, vintage finds, and unique treasures. Shop sustainable, save money.`;
}
