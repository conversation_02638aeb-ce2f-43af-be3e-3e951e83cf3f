/**
 * Layout Module - Scoped layout utilities
 * This module contains layout-related styles and utilities
 */

/* Container utilities */
.container {
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 2rem;
}

.container.small {
  max-width: 768px;
}

.container.large {
  max-width: 1400px;
}

.container.full {
  max-width: none;
  padding: 0;
}

/* Grid utilities */
.grid {
  display: grid;
  gap: 1rem;
}

.grid.gap-sm {
  gap: 0.5rem;
}

.grid.gap-lg {
  gap: 1.5rem;
}

.grid.gap-xl {
  gap: 2rem;
}

.grid.cols-1 {
  grid-template-columns: 1fr;
}

.grid.cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid.cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid.cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid.cols-auto {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid.cols-auto-sm {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.grid.cols-auto-lg {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Flex utilities */
.flex {
  display: flex;
}

.flex.column {
  flex-direction: column;
}

.flex.row {
  flex-direction: row;
}

.flex.wrap {
  flex-wrap: wrap;
}

.flex.nowrap {
  flex-wrap: nowrap;
}

.flex.center {
  align-items: center;
  justify-content: center;
}

.flex.items-center {
  align-items: center;
}

.flex.items-start {
  align-items: flex-start;
}

.flex.items-end {
  align-items: flex-end;
}

.flex.justify-center {
  justify-content: center;
}

.flex.justify-between {
  justify-content: space-between;
}

.flex.justify-around {
  justify-content: space-around;
}

.flex.justify-end {
  justify-content: flex-end;
}

.flex.gap-sm {
  gap: 0.5rem;
}

.flex.gap {
  gap: 1rem;
}

.flex.gap-lg {
  gap: 1.5rem;
}

.flex.gap-xl {
  gap: 2rem;
}

/* Spacing utilities */
.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.ml-auto { margin-left: auto; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }
.mr-auto { margin-right: auto; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 0.75rem; }
.pt-4 { padding-top: 1rem; }
.pt-6 { padding-top: 1.5rem; }
.pt-8 { padding-top: 2rem; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 0.25rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-3 { padding-bottom: 0.75rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-6 { padding-bottom: 1.5rem; }
.pb-8 { padding-bottom: 2rem; }

/* Width and height utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }

.h-full { height: 100%; }
.h-auto { height: auto; }
.h-fit { height: fit-content; }

.min-h-screen { min-height: 100vh; }

/* Text utilities */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Display utilities */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* Position utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Overflow utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

/* Responsive utilities */
@media (max-width: 640px) {
  .container {
    padding: 0 1rem;
  }

  .grid.cols-2,
  .grid.cols-3,
  .grid.cols-4 {
    grid-template-columns: 1fr;
  }

  .flex.column-mobile {
    flex-direction: column;
  }

  .hidden-mobile {
    display: none;
  }

  .block-mobile {
    display: block;
  }
}

@media (min-width: 641px) {
  .hidden-desktop {
    display: none;
  }

  .block-desktop {
    display: block;
  }
}
