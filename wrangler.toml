# Wrangler configuration for Cloudflare Pages Functions
name = "cheersmarketplace"
compatibility_date = "2024-01-01"

# Pages configuration
pages_build_output_dir = "dist"

# Environment variables for preview
[env.preview.vars]
# These should be set in the Cloudflare Pages dashboard
# GITHUB_TOKEN = "your_github_token"
# GITHUB_OWNER = "your_github_username"
# GITHUB_REPO = "your_repo_name"
# CLOUDFLARE_ZONE_ID = "your_zone_id"
# CLOUDFLARE_API_TOKEN = "your_api_token"
# CLOUDFLARE_BUILD_HOOK_URL = "your_build_hook_url"

# Environment variables for production
[env.production.vars]
# These should be set in the Cloudflare Pages dashboard
# GITHUB_TOKEN = "your_github_token"
# GITHUB_OWNER = "your_github_username"
# GITHUB_REPO = "your_repo_name"
# CLOUDFLARE_ZONE_ID = "your_zone_id"
# CLOUDFLARE_API_TOKEN = "your_api_token"
# CLOUDFLARE_BUILD_HOOK_URL = "your_build_hook_url"
