/**
 * Breadcrumb Generation Utilities
 * Automatically generates breadcrumb navigation based on URL paths
 */

import type { BreadcrumbItem } from './structuredData';

export interface BreadcrumbConfig {
  baseUrl?: string;
  homeLabel?: string;
  categoryLabels?: Record<string, string>;
  productNameResolver?: (slug: string) => Promise<string> | string;
}

const DEFAULT_CONFIG: Required<Omit<BreadcrumbConfig, 'productNameResolver'>> = {
  baseUrl: 'https://www.cheersmarketplace.com',
  homeLabel: 'Home',
  categoryLabels: {
    'electronics': 'Electronics',
    'clothing': 'Clothing',
    'home-garden': 'Home & Garden',
    'books': 'Books',
    'toys-games': 'Toys & Games',
    'sports-outdoors': 'Sports & Outdoors',
    'health-beauty': 'Health & Beauty',
    'automotive': 'Automotive'
  }
};

/**
 * Generate breadcrumbs from URL pathname
 */
export async function generateBreadcrumbs(
  pathname: string,
  config: BreadcrumbConfig = {}
): Promise<BreadcrumbItem[]> {
  const {
    baseUrl,
    homeLabel,
    categoryLabels,
    productNameResolver
  } = { ...DEFAULT_CONFIG, ...config };

  const breadcrumbs: BreadcrumbItem[] = [];
  
  // Always start with home
  breadcrumbs.push({
    name: homeLabel,
    url: baseUrl,
    position: 1
  });

  // Parse pathname
  const pathSegments = pathname.split('/').filter(segment => segment.length > 0);
  
  if (pathSegments.length === 0) {
    return breadcrumbs; // Just home page
  }

  let currentPath = '';
  let position = 2;

  for (let i = 0; i < pathSegments.length; i++) {
    const segment = pathSegments[i];
    const prevSegment = i > 0 ? pathSegments[i - 1] : null;
    const nextSegment = i < pathSegments.length - 1 ? pathSegments[i + 1] : null;

    currentPath += `/${segment}`;

    let name = segment;
    let url = `${baseUrl}${currentPath}`;

    // Skip the "category" segment in /products/category/[categoryName] URLs
    if (segment === 'category' && prevSegment === 'products') {
      continue;
    }

    // Handle specific routes
    switch (segment) {
      case 'products':
        name = 'Products';
        break;

      case 'about':
        name = 'About';
        break;

      case 'faq':
        name = 'FAQ';
        break;

      case 'terms':
        name = 'Terms & Conditions';
        break;

      case 'privacy':
        name = 'Privacy Policy';
        break;

      case 'admin':
        name = 'Admin';
        break;

      default:
        // Check if this is a category name after /products/category/
        if (prevSegment === 'category' && pathSegments[i - 2] === 'products') {
          // This is a category name, format it properly
          name = categoryLabels[segment] || formatSegmentName(segment);
          // Use the correct URL for category pages
          url = `${baseUrl}/products/category/${segment}`;
        }
        // Check if it's a category in the predefined list
        else if (categoryLabels[segment]) {
          name = categoryLabels[segment];
        }
        // Check if it's a product slug (last segment in /products/[slug])
        else if (prevSegment === 'products' && productNameResolver) {
          try {
            const productName = await productNameResolver(segment);
            name = productName || formatSegmentName(segment);
          } catch {
            name = formatSegmentName(segment);
          }
        }
        // Default formatting
        else {
          name = formatSegmentName(segment);
        }
        break;
    }

    breadcrumbs.push({
      name,
      url,
      position
    });

    position++;
  }

  return breadcrumbs;
}

/**
 * Format URL segment into readable name
 */
function formatSegmentName(segment: string): string {
  return segment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Generate breadcrumbs for product pages
 */
export async function generateProductBreadcrumbs(
  productSlug: string,
  productName: string,
  category?: string,
  config: BreadcrumbConfig = {}
): Promise<BreadcrumbItem[]> {
  const { baseUrl, homeLabel, categoryLabels } = { ...DEFAULT_CONFIG, ...config };

  const breadcrumbs: BreadcrumbItem[] = [
    {
      name: homeLabel,
      url: baseUrl,
      position: 1
    },
    {
      name: 'Products',
      url: `${baseUrl}/products/`,
      position: 2
    }
  ];

  // Add category if provided
  if (category) {
    const categoryName = categoryLabels[category] || formatSegmentName(category);
    const categorySlug = category.toLowerCase().replace(/[^a-z0-9]+/g, '-');
    breadcrumbs.push({
      name: categoryName,
      url: `${baseUrl}/products/category/${categorySlug}/`,
      position: 3
    });
  }

  // Add product
  breadcrumbs.push({
    name: productName,
    url: `${baseUrl}/products/${productSlug}/`,
    position: breadcrumbs.length + 1
  });

  return breadcrumbs;
}

/**
 * Generate breadcrumbs for category pages
 */
export function generateCategoryBreadcrumbs(
  category: string,
  config: BreadcrumbConfig = {}
): BreadcrumbItem[] {
  const { baseUrl, homeLabel, categoryLabels } = { ...DEFAULT_CONFIG, ...config };

  const categoryName = categoryLabels[category] || formatSegmentName(category);
  const categorySlug = category.toLowerCase().replace(/[^a-z0-9]+/g, '-');

  return [
    {
      name: homeLabel,
      url: baseUrl,
      position: 1
    },
    {
      name: 'Products',
      url: `${baseUrl}/products/`,
      position: 2
    },
    {
      name: categoryName,
      url: `${baseUrl}/products/category/${categorySlug}/`,
      position: 3
    }
  ];
}

/**
 * Generate breadcrumbs for search results
 */
export function generateSearchBreadcrumbs(
  searchQuery: string,
  config: BreadcrumbConfig = {}
): BreadcrumbItem[] {
  const { baseUrl, homeLabel } = { ...DEFAULT_CONFIG, ...config };

  return [
    {
      name: homeLabel,
      url: baseUrl,
      position: 1
    },
    {
      name: 'Products',
      url: `${baseUrl}/products`,
      position: 2
    },
    {
      name: `Search: "${searchQuery}"`,
      url: `${baseUrl}/products?search=${encodeURIComponent(searchQuery)}`,
      position: 3
    }
  ];
}

/**
 * Get product name from slug (to be implemented based on your data source)
 */
export async function getProductNameFromSlug(slug: string): Promise<string> {
  // This would typically fetch from your products data
  // For now, return formatted slug as fallback
  return formatSegmentName(slug);
}

/**
 * Validate breadcrumb items
 */
export function validateBreadcrumbs(items: BreadcrumbItem[]): BreadcrumbItem[] {
  return items
    .filter(item => item.name && item.url)
    .map((item, index) => ({
      ...item,
      position: index + 1
    }));
}
