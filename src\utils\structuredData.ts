/**
 * Structured Data Utilities for SEO
 * Generates JSON-LD structured data for various page types
 */

export interface Organization {
  name: string;
  url: string;
  logo: string;
  description: string;
  address?: {
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
  contactPoint?: {
    telephone: string;
    contactType: string;
    email: string;
  };
  sameAs?: string[];
}

export interface Product {
  id: string;
  name: string;
  description: string;
  image: string[];
  price: number;
  currency: string;
  availability: 'InStock' | 'OutOfStock' | 'PreOrder';
  condition: 'NewCondition' | 'UsedCondition' | 'RefurbishedCondition';
  brand?: string;
  category: string;
  sku?: string;
  gtin?: string;
  mpn?: string;
  url: string;
  offers?: {
    price: number;
    currency: string;
    availability: string;
    validFrom?: string;
    validThrough?: string;
  };
}

export interface BreadcrumbItem {
  name: string;
  url: string;
  position: number;
}

export interface WebPage {
  url: string;
  name: string;
  description: string;
  datePublished?: string;
  dateModified?: string;
  author?: string;
  image?: string;
}

/**
 * Generate Organization structured data
 */
export function generateOrganizationSchema(org: Organization): object {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": org.name,
    "url": org.url,
    "logo": {
      "@type": "ImageObject",
      "url": org.logo
    },
    "description": org.description,
    ...(org.address && {
      "address": {
        "@type": "PostalAddress",
        "streetAddress": org.address.streetAddress,
        "addressLocality": org.address.addressLocality,
        "addressRegion": org.address.addressRegion,
        "postalCode": org.address.postalCode,
        "addressCountry": org.address.addressCountry
      }
    }),
    ...(org.contactPoint && {
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": org.contactPoint.telephone,
        "contactType": org.contactPoint.contactType,
        "email": org.contactPoint.email
      }
    }),
    ...(org.sameAs && { "sameAs": org.sameAs })
  };
}

/**
 * Generate Product structured data
 */
export function generateProductSchema(product: Product): object {
  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "@id": product.url,
    "name": product.name,
    "description": product.description,
    "image": product.image,
    "url": product.url,
    "sku": product.sku || product.id,
    ...(product.gtin && { "gtin": product.gtin }),
    ...(product.mpn && { "mpn": product.mpn }),
    ...(product.brand && {
      "brand": {
        "@type": "Brand",
        "name": product.brand
      }
    }),
    "category": product.category,
    "offers": {
      "@type": "Offer",
      "price": product.price,
      "priceCurrency": product.currency,
      "availability": `https://schema.org/${product.availability}`,
      "itemCondition": `https://schema.org/${product.condition}`,
      "url": product.url,
      ...(product.offers?.validFrom && { "validFrom": product.offers.validFrom }),
      ...(product.offers?.validThrough && { "validThrough": product.offers.validThrough })
    }
  };
}

/**
 * Generate BreadcrumbList structured data
 */
export function generateBreadcrumbSchema(items: BreadcrumbItem[]): object {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map(item => ({
      "@type": "ListItem",
      "position": item.position,
      "name": item.name,
      "item": item.url
    }))
  };
}

/**
 * Generate WebPage structured data
 */
export function generateWebPageSchema(page: WebPage): object {
  return {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "@id": page.url,
    "url": page.url,
    "name": page.name,
    "description": page.description,
    ...(page.datePublished && { "datePublished": page.datePublished }),
    ...(page.dateModified && { "dateModified": page.dateModified }),
    ...(page.author && {
      "author": {
        "@type": "Person",
        "name": page.author
      }
    }),
    ...(page.image && {
      "primaryImageOfPage": {
        "@type": "ImageObject",
        "url": page.image
      }
    })
  };
}

/**
 * Generate WebSite structured data
 */
export function generateWebSiteSchema(name: string, url: string, description: string): object {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": `${url}#website`,
    "url": url,
    "name": name,
    "description": description,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${url}/products?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };
}

/**
 * Generate ItemList structured data for product listings
 */
export function generateItemListSchema(
  name: string,
  description: string,
  url: string,
  items: Array<{ name: string; url: string; position: number }>
): object {
  return {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": name,
    "description": description,
    "url": url,
    "numberOfItems": items.length,
    "itemListElement": items.map(item => ({
      "@type": "ListItem",
      "position": item.position,
      "name": item.name,
      "url": item.url
    }))
  };
}

/**
 * Generate FAQ structured data
 */
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>): object {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
}

/**
 * Generate LocalBusiness structured data (if applicable)
 */
export function generateLocalBusinessSchema(business: Organization & {
  openingHours?: string[];
  priceRange?: string;
  paymentAccepted?: string[];
}): object {
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": business.name,
    "url": business.url,
    "logo": business.logo,
    "description": business.description,
    ...(business.address && {
      "address": {
        "@type": "PostalAddress",
        "streetAddress": business.address.streetAddress,
        "addressLocality": business.address.addressLocality,
        "addressRegion": business.address.addressRegion,
        "postalCode": business.address.postalCode,
        "addressCountry": business.address.addressCountry
      }
    }),
    ...(business.contactPoint && {
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": business.contactPoint.telephone,
        "contactType": business.contactPoint.contactType,
        "email": business.contactPoint.email
      }
    }),
    ...(business.openingHours && { "openingHours": business.openingHours }),
    ...(business.priceRange && { "priceRange": business.priceRange }),
    ...(business.paymentAccepted && { "paymentAccepted": business.paymentAccepted }),
    ...(business.sameAs && { "sameAs": business.sameAs })
  };
}

/**
 * Combine multiple schemas into a single JSON-LD script
 */
export function combineSchemas(...schemas: object[]): string {
  if (schemas.length === 1) {
    return JSON.stringify(schemas[0], null, 0);
  }
  
  return JSON.stringify({
    "@context": "https://schema.org",
    "@graph": schemas
  }, null, 0);
}
