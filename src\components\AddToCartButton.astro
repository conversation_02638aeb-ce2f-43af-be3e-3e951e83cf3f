---
// Add to Cart Button Component for Snipcart
interface Props {
  productId: string;
  productName: string;
  productPrice: number;
  productImage?: string;
  productUrl?: string;
  productDescription?: string;
  productCategory?: string;
  maxQuantity?: number;
  customFields?: Array<{
    name: string;
    value?: string;
    options?: string;
    type?: 'dropdown' | 'text' | 'checkbox' | 'textarea' | 'readonly' | 'hidden';
    required?: boolean;
    placeholder?: string;
  }>;
  variant?: 'primary' | 'secondary';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

const {
  productId,
  productName,
  productPrice,
  productImage = '',
  productUrl,
  productDescription = '',
  productCategory = '',
  maxQuantity = 1,
  customFields = [],
  variant = 'primary',
  size = 'medium',
  fullWidth = false
} = Astro.props;

// Format price for display
const formattedPrice = `$${Number(productPrice).toFixed(2)}`;

// Generate Snipcart attributes
const snipcartAttributes: Record<string, string> = {
  'data-item-id': productId,
  'data-item-name': productName,
  'data-item-price': productPrice.toString(),
  'data-item-image': productImage,
  'data-item-description': productDescription,
  'data-item-max-quantity': maxQuantity.toString(),
};

// Add URL if provided, otherwise it will default to current page
if (productUrl) {
  snipcartAttributes['data-item-url'] = productUrl;
}

// Add categories if provided
if (productCategory) {
  snipcartAttributes['data-item-categories'] = productCategory;
}

// Add custom fields
customFields.forEach((field, index) => {
  const fieldIndex = index + 1;
  snipcartAttributes[`data-item-custom${fieldIndex}-name`] = field.name;
  
  if (field.value) {
    snipcartAttributes[`data-item-custom${fieldIndex}-value`] = field.value;
  }
  
  if (field.options) {
    snipcartAttributes[`data-item-custom${fieldIndex}-options`] = field.options;
  }
  
  if (field.type && field.type !== 'dropdown') {
    snipcartAttributes[`data-item-custom${fieldIndex}-type`] = field.type;
  }
  
  if (field.required) {
    snipcartAttributes[`data-item-custom${fieldIndex}-required`] = 'true';
  }
  
  if (field.placeholder) {
    snipcartAttributes[`data-item-custom${fieldIndex}-placeholder`] = field.placeholder;
  }
});
---

<button
  class={`add-to-cart-btn ${variant} ${size} ${fullWidth ? 'full-width' : ''} snipcart-add-item`}
  {...snipcartAttributes}
  aria-label={`Add ${productName} to cart for ${formattedPrice}`}
>
  <svg class="cart-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
    <circle cx="9" cy="21" r="1"></circle>
    <circle cx="20" cy="21" r="1"></circle>
    <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
  </svg>
  <span class="button-text">Add to Cart - {formattedPrice}</span>
</button>

<style>
  .add-to-cart-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
    line-height: 1;
    white-space: nowrap;
  }

  .add-to-cart-btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }

  /* Variants */
  .add-to-cart-btn.primary {
    background-color: var(--primary);
    color: white;
  }

  .add-to-cart-btn.primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
  }

  .add-to-cart-btn.secondary {
    background-color: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
  }

  .add-to-cart-btn.secondary:hover {
    background-color: var(--primary);
    color: white;
  }

  /* Sizes */
  .add-to-cart-btn.small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .add-to-cart-btn.small .cart-icon {
    width: 16px;
    height: 16px;
  }

  .add-to-cart-btn.medium {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  .add-to-cart-btn.large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
  }

  .add-to-cart-btn.large .cart-icon {
    width: 24px;
    height: 24px;
  }

  /* Full width */
  .add-to-cart-btn.full-width {
    width: 100%;
  }

  /* Icon styling */
  .cart-icon {
    flex-shrink: 0;
  }

  /* Mobile adjustments */
  @media (max-width: 640px) {
    .add-to-cart-btn {
      font-size: 0.875rem;
      padding: 0.75rem 1rem;
    }

    .add-to-cart-btn.large {
      font-size: 1rem;
      padding: 0.875rem 1.5rem;
    }

    .add-to-cart-btn:hover {
      transform: none;
    }
  }
</style>
