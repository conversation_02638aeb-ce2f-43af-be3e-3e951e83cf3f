/**
 * <PERSON>ton Module - Scoped button styles
 * This module contains all button-related styles that can be imported where needed
 */

/* Base button styles using CSS custom properties for consistency */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: var(--btn-padding);
  border: none;
  border-radius: var(--btn-border-radius);
  font-size: var(--btn-font-size);
  font-weight: var(--btn-font-weight);
  cursor: pointer;
  transition: var(--btn-transition);
  text-decoration: none;
  line-height: 1;
  white-space: nowrap;
  user-select: none;
  font-family: inherit;
}

/* Button variants */
.btn.primary {
  background: var(--primary);
  color: white;
}

.btn.primary:hover {
  background: var(--primary-dark);
  transform: var(--btn-hover-transform);
  box-shadow: var(--btn-hover-shadow);
}

.btn.secondary {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
}

.btn.secondary:hover {
  background: var(--border-light);
  border-color: var(--primary);
  transform: var(--btn-hover-transform);
}

.btn.outline {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn.outline:hover {
  background: var(--primary);
  color: white;
  transform: var(--btn-hover-transform);
}

/* Status variants */
.btn.success {
  background: var(--success);
  color: white;
}

.btn.success:hover {
  background: var(--success-dark);
  transform: var(--btn-hover-transform);
  box-shadow: var(--btn-hover-shadow);
}

.btn.danger {
  background: var(--danger);
  color: white;
}

.btn.danger:hover {
  background: var(--danger-dark);
  transform: var(--btn-hover-transform);
  box-shadow: var(--btn-hover-shadow);
}

.btn.warning {
  background: var(--warning);
  color: white;
}

.btn.warning:hover {
  background: var(--warning-dark);
  transform: var(--btn-hover-transform);
  box-shadow: var(--btn-hover-shadow);
}

/* Size variants */
.btn.small {
  padding: var(--btn-padding-sm);
  font-size: var(--btn-font-size-sm);
}

.btn.large {
  padding: var(--btn-padding-lg);
  font-size: var(--btn-font-size-lg);
}

/* State modifiers */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn.loading {
  pointer-events: none;
  opacity: 0.8;
}

.btn.full-width {
  width: 100%;
}

/* Icon buttons */
.btn.icon-only {
  padding: 0.75rem;
  aspect-ratio: 1;
}

.btn.icon-only.small {
  padding: 0.5rem;
}

.btn.icon-only.large {
  padding: 1rem;
}

/* Button groups */
.btn-group {
  display: inline-flex;
  border-radius: var(--btn-border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn-group .btn {
  border-radius: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--btn-border-radius);
  border-bottom-left-radius: var(--btn-border-radius);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--btn-border-radius);
  border-bottom-right-radius: var(--btn-border-radius);
  border-right: none;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.9375rem;
  }

  .btn.large {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  .btn.small {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
  }
}
