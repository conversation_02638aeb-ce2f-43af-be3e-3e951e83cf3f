---
/**
 * LCP (Largest Contentful Paint) Optimizer Component
 * Optimizes the loading of above-the-fold images to improve Core Web Vitals
 */

export interface Props {
  heroImage?: string;
  productImages?: string[];
  enablePreloading?: boolean;
  enablePriorityHints?: boolean;
  enableResourceHints?: boolean;
}

const {
  heroImage,
  productImages = [],
  enablePreloading = true,
  enablePriorityHints = true,
  enableResourceHints = true
} = Astro.props;

// Determine the most likely LCP candidate
const lcpCandidates = [
  heroImage,
  ...productImages.slice(0, 2) // First 2 product images are likely above-the-fold
].filter(Boolean);

// Generate optimized image URLs for different screen sizes
function generateOptimizedImageUrls(imageUrl: string) {
  if (!imageUrl) return {};
  
  // Check if it's a Bunny CDN image
  const isBunnyCDN = imageUrl.includes('b-cdn.net') || imageUrl.includes('bunnycdn.com');
  
  if (isBunnyCDN) {
    return {
      mobile: `${imageUrl}?width=480&height=360&quality=85&format=webp&fit=cover&crop=smart`,
      tablet: `${imageUrl}?width=768&height=576&quality=85&format=webp&fit=cover&crop=smart`,
      desktop: `${imageUrl}?width=1200&height=900&quality=85&format=webp&fit=cover&crop=smart`,
      large: `${imageUrl}?width=1600&height=1200&quality=85&format=webp&fit=cover&crop=smart`
    };
  }
  
  // For other images, return the original URL
  return {
    mobile: imageUrl,
    tablet: imageUrl,
    desktop: imageUrl,
    large: imageUrl
  };
}

// Generate preload links for LCP candidates
const preloadLinks = lcpCandidates.slice(0, 2).map(imageUrl => {
  const optimizedUrls = generateOptimizedImageUrls(imageUrl);
  return {
    original: imageUrl,
    ...optimizedUrls
  };
});
---

<!-- Resource Hints for LCP Optimization -->
{enableResourceHints && (
  <>
    <!-- Preconnect to image CDNs -->
    <link rel="preconnect" href="https://images.unsplash.com" crossorigin />
    <link rel="preconnect" href="https://bunnycdn.com" crossorigin />
    
    <!-- DNS prefetch for additional image domains -->
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net" />
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com" />
  </>
)}

<!-- Preload Critical Images for LCP -->
{enablePreloading && preloadLinks.map((imageSet, index) => (
  <>
    <!-- Simple preload for the original image without complex responsive logic -->
    <link
      rel="preload"
      as="image"
      href={imageSet.original}
      importance={index === 0 ? "high" : "auto"}
    />
  </>
))}

<!-- LCP Optimization Script -->
<script is:inline>
  (function() {
    'use strict';
    
    // LCP Optimization utilities
    const LCPOptimizer = {
      // Track LCP metrics
      lcpMetrics: {
        startTime: performance.now(),
        lcpTime: null,
        lcpElement: null,
        isOptimized: false
      },
      
      // Optimize image loading
      optimizeImageLoading: function() {
        const images = document.querySelectorAll('img[data-lcp-candidate="true"]');
        
        images.forEach((img, index) => {
          // Set high priority for first image
          if (index === 0) {
            img.setAttribute('fetchpriority', 'high');
            img.setAttribute('loading', 'eager');
          }
          
          // Add intersection observer for lazy loading optimization
          if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
              entries.forEach(entry => {
                if (entry.isIntersecting) {
                  const image = entry.target;
                  
                  // Optimize image loading
                  if (image.dataset.src) {
                    image.src = image.dataset.src;
                    image.removeAttribute('data-src');
                  }
                  
                  // Add loaded class for styling
                  image.onload = () => {
                    image.classList.add('lcp-loaded');
                    this.trackLCPImprovement(image);
                  };
                  
                  observer.unobserve(image);
                }
              });
            }, {
              rootMargin: '50px 0px',
              threshold: 0.1
            });
            
            observer.observe(img);
          }
        });
      },
      
      // Track LCP improvements
      trackLCPImprovement: function(element) {
        if (this.lcpMetrics.lcpElement === element) {
          this.lcpMetrics.isOptimized = true;
          console.log('LCP element optimized:', {
            element: element,
            loadTime: performance.now() - this.lcpMetrics.startTime
          });
        }
      },
      
      // Measure LCP using PerformanceObserver
      measureLCP: function() {
        if ('PerformanceObserver' in window) {
          try {
            const observer = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              const lastEntry = entries[entries.length - 1];
              
              this.lcpMetrics.lcpTime = lastEntry.startTime;
              this.lcpMetrics.lcpElement = lastEntry.element;
              
              console.log('LCP measured:', {
                time: lastEntry.startTime,
                element: lastEntry.element,
                url: lastEntry.url
              });
              
              // Disconnect observer after getting LCP
              observer.disconnect();
            });
            
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
          } catch (e) {
            console.warn('LCP measurement not supported:', e);
          }
        }
      },
      
      // Optimize critical resource loading
      optimizeCriticalResources: function() {
        // Using system fonts only - no external font preloading needed
        // This eliminates 404 errors and improves performance
        
        // Optimize CSS delivery
        const nonCriticalCSS = document.querySelectorAll('link[rel="stylesheet"][media="print"]');
        nonCriticalCSS.forEach(link => {
          link.onload = () => {
            link.media = 'all';
          };
        });
      },
      
      // Initialize LCP optimization
      init: function() {
        // Start LCP measurement
        this.measureLCP();
        
        // Optimize image loading
        this.optimizeImageLoading();
        
        // Optimize critical resources
        this.optimizeCriticalResources();
        
        // Report optimization status
        setTimeout(() => {
          console.log('LCP Optimization Status:', this.lcpMetrics);
        }, 3000);
      }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => LCPOptimizer.init());
    } else {
      LCPOptimizer.init();
    }
    
    // Expose for debugging
    window.LCPOptimizer = LCPOptimizer;
  })();
</script>

<!-- Critical CSS for LCP elements -->
<style>
  /* Optimize LCP candidate elements */
  img[data-lcp-candidate="true"] {
    /* Prevent layout shift */
    width: 100%;
    height: auto;
    
    /* Optimize rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    
    /* Smooth loading transition */
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }
  
  img[data-lcp-candidate="true"].lcp-loaded {
    opacity: 1;
  }
  
  /* Optimize for different screen sizes */
  @media (max-width: 480px) {
    img[data-lcp-candidate="true"] {
      max-width: 480px;
      max-height: 360px;
    }
  }
  
  @media (min-width: 481px) and (max-width: 768px) {
    img[data-lcp-candidate="true"] {
      max-width: 768px;
      max-height: 576px;
    }
  }
  
  @media (min-width: 769px) and (max-width: 1200px) {
    img[data-lcp-candidate="true"] {
      max-width: 1200px;
      max-height: 900px;
    }
  }
  
  @media (min-width: 1201px) {
    img[data-lcp-candidate="true"] {
      max-width: 1600px;
      max-height: 1200px;
    }
  }
  
  /* Prevent cumulative layout shift */
  .lcp-container {
    position: relative;
    overflow: hidden;
  }
  
  .lcp-container::before {
    content: '';
    display: block;
    padding-bottom: 75%; /* 4:3 aspect ratio */
  }
  
  .lcp-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  /* Performance optimizations */
  @media (prefers-reduced-motion: reduce) {
    img[data-lcp-candidate="true"] {
      transition: none !important;
    }
  }
  
  /* High DPI optimization */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    img[data-lcp-candidate="true"] {
      image-rendering: -webkit-optimize-contrast;
    }
  }
</style>
