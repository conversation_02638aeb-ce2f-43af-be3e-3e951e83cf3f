/**
 * CSS Purging Utility
 * Removes unused CSS to reduce bundle size and improve performance
 */

export interface CSSPurgeConfig {
  preserveSelectors: string[];
  removeUnusedKeyframes: boolean;
  removeUnusedFontFaces: boolean;
  removeComments: boolean;
  minify: boolean;
}

export interface CSSAnalysis {
  totalRules: number;
  usedRules: number;
  unusedRules: number;
  sizeBefore: number;
  sizeAfter: number;
  savings: number;
}

/**
 * Default configuration for CSS purging
 */
export const DEFAULT_PURGE_CONFIG: CSSPurgeConfig = {
  preserveSelectors: [
    // Snipcart selectors
    '.snipcart-*',
    '[data-snipcart-*]',
    // Dynamic selectors
    '.js-*',
    '.is-*',
    '.has-*',
    // State selectors
    ':hover',
    ':focus',
    ':active',
    ':visited',
    // Responsive selectors
    '@media',
    // Animation selectors
    '@keyframes',
    // Print selectors
    '@print'
  ],
  removeUnusedKeyframes: true,
  removeUnusedFontFaces: true,
  removeComments: true,
  minify: true
};

/**
 * Extract selectors from HTML content
 */
export function extractSelectorsFromHTML(html: string): Set<string> {
  const selectors = new Set<string>();
  
  // Extract class names
  const classMatches = html.match(/class\s*=\s*["']([^"']+)["']/g);
  if (classMatches) {
    classMatches.forEach(match => {
      const classes = match.replace(/class\s*=\s*["']/, '').replace(/["']$/, '');
      classes.split(/\s+/).forEach(className => {
        if (className.trim()) {
          selectors.add(`.${className.trim()}`);
        }
      });
    });
  }
  
  // Extract IDs
  const idMatches = html.match(/id\s*=\s*["']([^"']+)["']/g);
  if (idMatches) {
    idMatches.forEach(match => {
      const id = match.replace(/id\s*=\s*["']/, '').replace(/["']$/, '');
      if (id.trim()) {
        selectors.add(`#${id.trim()}`);
      }
    });
  }
  
  // Extract data attributes
  const dataMatches = html.match(/data-[a-zA-Z0-9-]+/g);
  if (dataMatches) {
    dataMatches.forEach(attr => {
      selectors.add(`[${attr}]`);
    });
  }
  
  // Extract tag names
  const tagMatches = html.match(/<([a-zA-Z][a-zA-Z0-9]*)/g);
  if (tagMatches) {
    tagMatches.forEach(match => {
      const tag = match.replace('<', '');
      selectors.add(tag.toLowerCase());
    });
  }
  
  return selectors;
}

/**
 * Parse CSS and extract rules
 */
export function parseCSSRules(css: string): Array<{ selector: string; rule: string; type: 'rule' | 'keyframe' | 'fontface' | 'media' }> {
  const rules: Array<{ selector: string; rule: string; type: 'rule' | 'keyframe' | 'fontface' | 'media' }> = [];
  
  // Remove comments first
  const cleanCSS = css.replace(/\/\*[\s\S]*?\*\//g, '');
  
  // Extract @keyframes
  const keyframeMatches = cleanCSS.match(/@keyframes\s+([^{]+)\s*{[^{}]*(?:{[^{}]*}[^{}]*)*}/g);
  if (keyframeMatches) {
    keyframeMatches.forEach(match => {
      const nameMatch = match.match(/@keyframes\s+([^{]+)/);
      if (nameMatch) {
        rules.push({
          selector: nameMatch[1].trim(),
          rule: match,
          type: 'keyframe'
        });
      }
    });
  }
  
  // Extract @font-face
  const fontFaceMatches = cleanCSS.match(/@font-face\s*{[^{}]*(?:{[^{}]*}[^{}]*)*}/g);
  if (fontFaceMatches) {
    fontFaceMatches.forEach(match => {
      rules.push({
        selector: '@font-face',
        rule: match,
        type: 'fontface'
      });
    });
  }
  
  // Extract @media queries
  const mediaMatches = cleanCSS.match(/@media[^{]+{[^{}]*(?:{[^{}]*}[^{}]*)*}/g);
  if (mediaMatches) {
    mediaMatches.forEach(match => {
      const conditionMatch = match.match(/@media([^{]+)/);
      if (conditionMatch) {
        rules.push({
          selector: `@media${conditionMatch[1]}`,
          rule: match,
          type: 'media'
        });
      }
    });
  }
  
  // Extract regular CSS rules
  const ruleMatches = cleanCSS.match(/[^{}]+{[^{}]*}/g);
  if (ruleMatches) {
    ruleMatches.forEach(match => {
      // Skip @rules that we've already processed
      if (match.startsWith('@keyframes') || match.startsWith('@font-face') || match.startsWith('@media')) {
        return;
      }
      
      const selectorMatch = match.match(/^([^{]+)/);
      if (selectorMatch) {
        rules.push({
          selector: selectorMatch[1].trim(),
          rule: match,
          type: 'rule'
        });
      }
    });
  }
  
  return rules;
}

/**
 * Check if a selector is used in the HTML
 */
export function isSelectorUsed(selector: string, usedSelectors: Set<string>, preserveSelectors: string[]): boolean {
  // Check if selector should be preserved
  for (const preserve of preserveSelectors) {
    if (preserve.includes('*')) {
      const pattern = preserve.replace(/\*/g, '.*');
      const regex = new RegExp(`^${pattern}$`);
      if (regex.test(selector)) {
        return true;
      }
    } else if (selector.includes(preserve)) {
      return true;
    }
  }
  
  // Check if selector is directly used
  if (usedSelectors.has(selector)) {
    return true;
  }
  
  // Check for complex selectors
  const selectorParts = selector.split(/[\s>+~,]/);
  for (const part of selectorParts) {
    const trimmedPart = part.trim();
    if (trimmedPart && usedSelectors.has(trimmedPart)) {
      return true;
    }
  }
  
  // Check for pseudo-selectors
  const baseSelectorMatch = selector.match(/^([^:]+)/);
  if (baseSelectorMatch) {
    const baseSelector = baseSelectorMatch[1].trim();
    if (usedSelectors.has(baseSelector)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Purge unused CSS
 */
export function purgeCSS(css: string, html: string, config: Partial<CSSPurgeConfig> = {}): { css: string; analysis: CSSAnalysis } {
  const finalConfig = { ...DEFAULT_PURGE_CONFIG, ...config };
  const usedSelectors = extractSelectorsFromHTML(html);
  const rules = parseCSSRules(css);
  
  const usedRules: string[] = [];
  let unusedCount = 0;
  
  rules.forEach(({ selector, rule, type }) => {
    let shouldKeep = false;
    
    switch (type) {
      case 'rule':
        shouldKeep = isSelectorUsed(selector, usedSelectors, finalConfig.preserveSelectors);
        break;
      case 'keyframe':
        // Keep keyframes if they're referenced or if preservation is disabled
        shouldKeep = !finalConfig.removeUnusedKeyframes || 
                    css.includes(`animation:`) && css.includes(selector) ||
                    css.includes(`animation-name:`) && css.includes(selector);
        break;
      case 'fontface':
        // Keep font faces if preservation is disabled or if they're likely used
        shouldKeep = !finalConfig.removeUnusedFontFaces || 
                    css.includes('font-family:');
        break;
      case 'media':
        // Always keep media queries for now (more complex analysis needed)
        shouldKeep = true;
        break;
    }
    
    if (shouldKeep) {
      usedRules.push(rule);
    } else {
      unusedCount++;
    }
  });
  
  let purgedCSS = usedRules.join('\n');
  
  // Remove comments if configured
  if (finalConfig.removeComments) {
    purgedCSS = purgedCSS.replace(/\/\*[\s\S]*?\*\//g, '');
  }
  
  // Minify if configured
  if (finalConfig.minify) {
    purgedCSS = minifyCSS(purgedCSS);
  }
  
  const analysis: CSSAnalysis = {
    totalRules: rules.length,
    usedRules: usedRules.length,
    unusedRules: unusedCount,
    sizeBefore: css.length,
    sizeAfter: purgedCSS.length,
    savings: css.length - purgedCSS.length
  };
  
  return { css: purgedCSS, analysis };
}

/**
 * Simple CSS minification
 */
export function minifyCSS(css: string): string {
  return css
    // Remove extra whitespace
    .replace(/\s+/g, ' ')
    // Remove whitespace around special characters
    .replace(/\s*{\s*/g, '{')
    .replace(/\s*}\s*/g, '}')
    .replace(/\s*;\s*/g, ';')
    .replace(/\s*:\s*/g, ':')
    .replace(/\s*,\s*/g, ',')
    // Remove trailing semicolons
    .replace(/;}/g, '}')
    // Remove leading/trailing whitespace
    .trim();
}

/**
 * Analyze CSS usage across multiple HTML files
 */
export function analyzeCSSUsage(css: string, htmlFiles: string[]): CSSAnalysis {
  const allSelectors = new Set<string>();
  
  // Combine selectors from all HTML files
  htmlFiles.forEach(html => {
    const selectors = extractSelectorsFromHTML(html);
    selectors.forEach(selector => allSelectors.add(selector));
  });
  
  const rules = parseCSSRules(css);
  let usedCount = 0;
  
  rules.forEach(({ selector }) => {
    if (isSelectorUsed(selector, allSelectors, DEFAULT_PURGE_CONFIG.preserveSelectors)) {
      usedCount++;
    }
  });
  
  return {
    totalRules: rules.length,
    usedRules: usedCount,
    unusedRules: rules.length - usedCount,
    sizeBefore: css.length,
    sizeAfter: 0, // Not calculated in analysis mode
    savings: 0 // Not calculated in analysis mode
  };
}

/**
 * Generate critical CSS from full CSS based on above-the-fold selectors
 */
export function extractCriticalCSS(css: string, criticalSelectors: string[]): string {
  const rules = parseCSSRules(css);
  const criticalRules: string[] = [];
  const criticalSelectorSet = new Set(criticalSelectors);
  
  rules.forEach(({ selector, rule, type }) => {
    if (type === 'rule' && isSelectorUsed(selector, criticalSelectorSet, [])) {
      criticalRules.push(rule);
    }
  });
  
  return minifyCSS(criticalRules.join('\n'));
}

/**
 * Split CSS into critical and non-critical parts
 */
export function splitCSS(css: string, criticalSelectors: string[]): { critical: string; nonCritical: string } {
  const rules = parseCSSRules(css);
  const criticalRules: string[] = [];
  const nonCriticalRules: string[] = [];
  const criticalSelectorSet = new Set(criticalSelectors);
  
  rules.forEach(({ selector, rule, type }) => {
    if (type === 'rule' && isSelectorUsed(selector, criticalSelectorSet, [])) {
      criticalRules.push(rule);
    } else {
      nonCriticalRules.push(rule);
    }
  });
  
  return {
    critical: minifyCSS(criticalRules.join('\n')),
    nonCritical: minifyCSS(nonCriticalRules.join('\n'))
  };
}
