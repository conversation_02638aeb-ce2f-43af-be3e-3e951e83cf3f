---
// Admin Category Manager Component - Category management interface
---

<div id="section-categories" class="admin-section">
  <div class="form-header">
    <h2>Manage Categories</h2>
    <div class="form-actions">
      <button id="add-category-btn" class="btn-primary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
        </svg>
        Add Category
      </button>
    </div>
  </div>

  <div class="categories-container">
    <div class="categories-list">
      <h3>Current Categories</h3>
      <div id="categories-grid" class="categories-grid">
        <!-- Categories will be loaded here -->
      </div>
    </div>

    <div class="category-form-container" id="category-form-container" style="display: none;">
      <h3 id="category-form-title">Add New Category</h3>
      <form id="category-form" class="category-form">
        <div class="form-group">
          <label for="category-name">Category Name *</label>
          <input type="text" id="category-name" name="name" required class="form-input" />
        </div>
        <div class="form-group">
          <label for="category-description">Description</label>
          <textarea id="category-description" name="description" class="form-input" rows="3"></textarea>
        </div>
        <div class="form-actions">
          <button type="button" id="cancel-category" class="btn-secondary">Cancel</button>
          <button type="button" id="save-category" class="btn-primary">Save Category</button>
        </div>
      </form>
    </div>
  </div>
</div>
