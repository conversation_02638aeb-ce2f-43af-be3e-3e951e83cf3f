/**
 * Forms Module - Scoped form styles
 * This module contains all form-related styles for consistent form elements
 */

/* Base form styles */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.form-label {
  font-weight: 600;
  color: var(--text);
  font-size: 0.875rem;
  letter-spacing: -0.01em;
}

.form-label.required::after {
  content: ' *';
  color: var(--danger);
}

/* Input styles */
.form-input,
.form-select,
.form-textarea {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background: var(--background);
  color: var(--text);
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.2s ease;
  width: 100%;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background: var(--border-light);
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* Input variants */
.form-input.small,
.form-select.small {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.form-input.large,
.form-select.large {
  padding: 1rem 1.25rem;
  font-size: 1.125rem;
}

/* Textarea specific */
.form-textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
}

.form-textarea.small {
  min-height: 80px;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.form-textarea.large {
  min-height: 120px;
  padding: 1rem 1.25rem;
  font-size: 1.125rem;
}

/* Select specific */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  appearance: none;
}

/* Checkbox and radio styles */
.form-checkbox,
.form-radio {
  width: 1rem;
  height: 1rem;
  border: 1px solid var(--border);
  background: var(--background);
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-checkbox {
  border-radius: var(--radius-sm);
}

.form-radio {
  border-radius: 50%;
}

.form-checkbox:checked,
.form-radio:checked {
  background: var(--primary);
  border-color: var(--primary);
}

.form-checkbox:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

.form-radio:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

/* Form validation states */
.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--danger);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-input.success,
.form-select.success,
.form-textarea.success {
  border-color: var(--success);
  box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
}

/* Help text */
.form-help {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.form-error {
  font-size: 0.75rem;
  color: var(--danger);
  line-height: 1.4;
  font-weight: 500;
}

.form-success {
  font-size: 0.75rem;
  color: var(--success);
  line-height: 1.4;
  font-weight: 500;
}

/* Form layouts */
.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.form-row.two-columns {
  grid-template-columns: 1fr 1fr;
}

.form-row.three-columns {
  grid-template-columns: 1fr 1fr 1fr;
}

/* Responsive form layouts */
@media (max-width: 640px) {
  .form-row.two-columns,
  .form-row.three-columns {
    grid-template-columns: 1fr;
  }

  .form-input,
  .form-select,
  .form-textarea {
    padding: 0.625rem 0.875rem;
    font-size: 0.9375rem;
  }
}

/* Form sections */
.form-section {
  padding: 1.5rem;
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  background: var(--card-bg);
  margin-bottom: 1.5rem;
}

.form-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text);
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border);
}
