/* Header and navigation styles */
.site-header {
  background: var(--light-background);
  color: var(--text);
  box-shadow: var(--shadow-sm);
  padding: 0;
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 50;
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
}

.header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
  padding: 0 2rem;
}

.site-header .logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text);
  gap: 0.75rem;
  transition: opacity 0.2s ease;
}

.site-header .logo:hover {
  opacity: 0.8;
}



.site-header .main-nav {
  display: flex;
  gap: 3rem;
  align-items: center;
  flex: 1;
  justify-content: center;
  margin: 0 2rem;
}

.site-header .main-nav a {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  letter-spacing: -0.01em;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  position: relative;
}

.site-header .main-nav a:hover {
  color: var(--primary);
  background: var(--border-light);
}

/* Mobile hamburger menu - Styles moved to Header.astro for better specificity */
/* Keeping this comment for reference - actual styles are in Header.astro component */

/* Mobile responsive */
@media (max-width: 768px) {
  .header-flex {
    padding: 0 1.5rem;
    min-height: 56px;
    position: relative;
  }

  /* Mobile menu toggle styles moved to Header.astro component */

  /* Mobile nav styles moved to Header.astro component for better specificity */
  /* All mobile navigation styles are now in Header.astro to prevent conflicts */

  .site-header .main-nav a {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    border-radius: 0;
    border-bottom: 1px solid var(--border-light);
  }

  .site-header .main-nav a:last-child {
    border-bottom: none;
  }


}

@media (max-width: 480px) {
  .header-flex {
    padding: 0 1rem;
    min-height: 56px;
  }

  .site-header .main-nav a {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
  }



  /* Mobile menu toggle responsive styles moved to Header.astro component */
}
