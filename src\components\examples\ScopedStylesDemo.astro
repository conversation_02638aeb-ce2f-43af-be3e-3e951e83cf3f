---
// Scoped Styles Demo Component
// This component demonstrates both Astro's automatic scoped styles and CSS modules usage

export interface Props {
  title?: string;
  showExamples?: boolean;
}

const { 
  title = "CSS Scoping Demo", 
  showExamples = true 
} = Astro.props;
---

<div class="demo-container">
  <h2 class="demo-title">{title}</h2>
  
  {showExamples && (
    <div class="examples-section">
      <!-- CSS Modules Examples -->
      <div class="example-group">
        <h3 class="example-title">CSS Modules (Global Classes)</h3>
        <div class="flex gap items-center mb-4">
          <button class="btn primary">Primary Button</button>
          <button class="btn secondary">Secondary Button</button>
          <button class="btn outline small">Small Outline</button>
        </div>
        
        <div class="grid cols-2 gap">
          <div class="form-group">
            <label class="form-label">Name</label>
            <input class="form-input" type="text" placeholder="Enter your name" />
          </div>
          <div class="form-group">
            <label class="form-label">Email</label>
            <input class="form-input" type="email" placeholder="Enter your email" />
          </div>
        </div>
      </div>

      <!-- Scoped Styles Examples -->
      <div class="example-group">
        <h3 class="example-title">Scoped Styles (Component-specific)</h3>
        <div class="scoped-buttons">
          <button class="custom-btn primary-custom">Custom Primary</button>
          <button class="custom-btn secondary-custom">Custom Secondary</button>
        </div>
        
        <div class="custom-card">
          <h4 class="card-title">Scoped Card</h4>
          <p class="card-content">
            This card uses scoped styles that won't affect other components.
            The styles are automatically prefixed with unique identifiers.
          </p>
        </div>
      </div>
    </div>
  )}
</div>

<style>
  /* These styles are automatically scoped to this component only */
  .demo-container {
    padding: 2rem;
    background: var(--card-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    margin: 1rem 0;
  }

  .demo-title {
    color: var(--text);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
    text-align: center;
    border-bottom: 2px solid var(--primary);
    padding-bottom: 0.5rem;
  }

  .examples-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .example-group {
    padding: 1.5rem;
    background: var(--background);
    border-radius: var(--radius);
    border: 1px solid var(--border-light);
  }

  .example-title {
    color: var(--text);
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    color: var(--primary);
  }

  /* Custom scoped button styles */
  .scoped-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
  }

  .custom-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
  }

  .custom-btn.primary-custom {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
  }

  .custom-btn.primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(var(--primary-rgb), 0.4);
  }

  .custom-btn.secondary-custom {
    background: var(--light-background);
    color: var(--text);
    border: 2px solid var(--border);
    position: relative;
    overflow: hidden;
  }

  .custom-btn.secondary-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(var(--primary-rgb), 0.1), transparent);
    transition: left 0.5s ease;
  }

  .custom-btn.secondary-custom:hover::before {
    left: 100%;
  }

  .custom-btn.secondary-custom:hover {
    border-color: var(--primary);
    color: var(--primary);
  }

  /* Custom scoped card styles */
  .custom-card {
    background: linear-gradient(135deg, var(--card-bg), var(--light-background));
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
  }

  .custom-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--primary-dark));
  }

  .card-title {
    color: var(--text);
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
  }

  .card-content {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
  }

  /* Responsive adjustments - these are also scoped */
  @media (max-width: 640px) {
    .demo-container {
      padding: 1.5rem;
    }

    .examples-section {
      gap: 1.5rem;
    }

    .example-group {
      padding: 1rem;
    }

    .scoped-buttons {
      flex-direction: column;
      align-items: stretch;
    }

    .custom-btn {
      text-align: center;
    }
  }
</style>

<!-- 
  This component demonstrates:
  
  1. Astro's Automatic Scoped Styles:
     - All styles in the <style> tag are automatically scoped
     - Class names get unique identifiers like [data-astro-cid-xyz]
     - No style conflicts with other components
  
  2. CSS Modules Usage:
     - Global utility classes from our modules (btn, form-input, grid, etc.)
     - Can be combined with scoped styles
     - Provides consistent design system
  
  3. Best Practices:
     - Use CSS modules for common patterns (buttons, forms, layout)
     - Use scoped styles for component-specific styling
     - Combine both approaches for maximum flexibility
     - Responsive design within scoped styles
-->
