// Products Pagination JavaScript
class ProductsPagination {
  constructor() {
    this.currentPage = 1;
    this.itemsPerPage = 24; // Default items per page
    this.allProducts = [];
    this.filteredProducts = [];
    this.init();
  }

  init() {
    // Get all product cards
    this.allProducts = Array.from(document.querySelectorAll('.product-card'));
    this.filteredProducts = [...this.allProducts];

    // Bind events
    this.bindEvents();

    // Initial render
    this.updateDisplay();
  }

  bindEvents() {
    // Items per page selector
    const itemsPerPageSelect = document.getElementById('items-per-page-select');
    if (itemsPerPageSelect) {
      itemsPerPageSelect.addEventListener('change', (e) => {
        this.itemsPerPage = parseInt(e.target.value);
        this.currentPage = 1; // Reset to first page
        this.updateDisplay();
      });
    }

    // Listen for filter changes from the products-filter.js
    document.addEventListener('productsFiltered', (e) => {
      this.filteredProducts = e.detail.visibleProducts || [];
      this.currentPage = 1; // Reset to first page when filtering
      this.updateDisplay();
    });
  }

  updateDisplay() {
    this.hideAllProducts();
    this.showCurrentPageProducts();
    this.updatePaginationControls();
    this.updatePaginationInfo();
  }

  hideAllProducts() {
    this.allProducts.forEach(product => {
      product.style.display = 'none';
    });
  }

  showCurrentPageProducts() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageProducts = this.filteredProducts.slice(startIndex, endIndex);

    pageProducts.forEach(product => {
      product.style.display = '';
    });

    // Show/hide no results message
    const noResults = document.getElementById('no-results');
    if (noResults) {
      noResults.style.display = this.filteredProducts.length === 0 ? 'block' : 'none';
    }
  }

  updatePaginationControls() {
    const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
    const paginationControls = document.getElementById('pagination-controls');
    
    if (!paginationControls || totalPages <= 1) {
      if (paginationControls) paginationControls.innerHTML = '';
      return;
    }

    let html = '';

    // Previous button
    html += `<button class="page-btn" ${this.currentPage === 1 ? 'disabled' : ''} onclick="productsPagination.goToPage(${this.currentPage - 1})">‹</button>`;

    // Page numbers with smart truncation
    const maxVisiblePages = 7;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    // Adjust start if we're near the end
    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // First page and ellipsis
    if (startPage > 1) {
      html += `<button class="page-btn" onclick="productsPagination.goToPage(1)">1</button>`;
      if (startPage > 2) {
        html += `<span class="page-ellipsis">...</span>`;
      }
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      html += `<button class="page-btn ${i === this.currentPage ? 'active' : ''}" onclick="productsPagination.goToPage(${i})">${i}</button>`;
    }

    // Last page and ellipsis
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        html += `<span class="page-ellipsis">...</span>`;
      }
      html += `<button class="page-btn" onclick="productsPagination.goToPage(${totalPages})">${totalPages}</button>`;
    }

    // Next button
    html += `<button class="page-btn" ${this.currentPage === totalPages ? 'disabled' : ''} onclick="productsPagination.goToPage(${this.currentPage + 1})">›</button>`;

    paginationControls.innerHTML = html;
  }

  updatePaginationInfo() {
    const paginationText = document.getElementById('pagination-text');
    if (!paginationText) return;

    const totalProducts = this.filteredProducts.length;
    if (totalProducts === 0) {
      paginationText.textContent = 'No products found';
      return;
    }

    const startIndex = (this.currentPage - 1) * this.itemsPerPage + 1;
    const endIndex = Math.min(this.currentPage * this.itemsPerPage, totalProducts);
    
    paginationText.textContent = `Showing ${startIndex}-${endIndex} of ${totalProducts} products`;
  }

  goToPage(page) {
    const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
    if (page < 1 || page > totalPages) return;
    
    this.currentPage = page;
    this.updateDisplay();
    
    // Scroll to top of products section
    const productsSection = document.querySelector('.products-section');
    if (productsSection) {
      productsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  // Method to be called by external filters
  updateFilteredProducts(visibleProducts) {
    this.filteredProducts = visibleProducts;
    this.currentPage = 1;
    this.updateDisplay();
  }
}

// Initialize pagination when DOM is ready
function initializePagination() {
  const productsGrid = document.querySelector('.products-grid');
  const itemsPerPageSelect = document.getElementById('items-per-page-select');

  if (productsGrid && itemsPerPageSelect) {
    window.productsPagination = new ProductsPagination();
    return true;
  }
  return false;
}

// Try multiple times to initialize pagination
document.addEventListener('DOMContentLoaded', () => {
  if (!initializePagination()) {
    // Retry after a short delay
    setTimeout(() => {
      if (!initializePagination()) {
        // Retry one more time after page load
        window.addEventListener('load', () => {
          setTimeout(initializePagination, 100);
        });
      }
    }, 100);
  }
});
