---
/**
 * Enhanced Image Component with Advanced Optimization
 * Reduces image waste by implementing smart sizing and format selection
 */
import { optimizeImage, generateSizesAttribute, type ImageOptimizationConfig } from '../utils/advancedImageOptimization';

export interface Props {
  src: string;
  alt: string;
  context?: 'product-grid' | 'product-detail' | 'hero';
  loading?: 'lazy' | 'eager';
  fetchpriority?: 'high' | 'low' | 'auto';
  class?: string;
  style?: string;
  quality?: number;
  format?: 'webp' | 'avif' | 'auto';
  enablePlaceholder?: boolean;
  fallbackSrc?: string;
  width?: number;
  height?: number;
  isLCPCandidate?: boolean;
}

const {
  src,
  alt,
  context = 'product-grid',
  loading = 'lazy',
  fetchpriority = 'auto',
  class: className = '',
  style = '',
  quality = 85,
  format = 'auto',
  enablePlaceholder = true,
  fallbackSrc,
  width,
  height,
  isLCPCandidate = false
} = Astro.props;

// Create optimization config
const config: Partial<ImageOptimizationConfig> = {
  quality,
  format,
  enablePlaceholder,
  enableLazyLoading: loading === 'lazy',
  enableResponsive: true
};

// Optimize the image
const optimizedImage = optimizeImage(src, context, config);

// Use provided dimensions or optimized ones
const finalWidth = width || optimizedImage.width;
const finalHeight = height || optimizedImage.height;
const finalSizes = generateSizesAttribute(context);

// Generate unique ID for intersection observer
const imageId = `img-${Math.random().toString(36).substr(2, 9)}`;
---

<div class={`enhanced-image-container ${className}`} style={style}>
  {enablePlaceholder && optimizedImage.placeholder && (
    <img
      src={optimizedImage.placeholder}
      alt=""
      class="image-placeholder"
      aria-hidden="true"
      loading="eager"
      decoding="async"
      width={finalWidth}
      height={finalHeight}
    />
  )}
  
  <img
    id={imageId}
    src={optimizedImage.src}
    srcset={optimizedImage.srcset}
    sizes={finalSizes}
    alt={alt}
    width={finalWidth}
    height={finalHeight}
    loading={loading}
    fetchpriority={fetchpriority}
    class={`enhanced-image ${loading === 'lazy' ? 'lazy-image' : ''}`}
    decoding="async"
    data-lcp-candidate={isLCPCandidate ? "true" : "false"}
    onerror={fallbackSrc ? `this.src='${fallbackSrc}'; this.parentElement.classList.add('error')` : "this.parentElement.classList.add('error')"}
  />
</div>

<style>
  .enhanced-image-container {
    position: relative;
    overflow: hidden;
    background-color: #f8fafc;
    border-radius: 0.5rem;
  }

  .image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: blur(10px);
    transform: scale(1.1);
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  .enhanced-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
    position: relative;
    z-index: 2;
  }

  .lazy-image {
    opacity: 0;
  }

  .enhanced-image-container.loaded .image-placeholder {
    opacity: 0;
  }

  .enhanced-image-container.loaded .enhanced-image {
    opacity: 1;
  }

  .enhanced-image-container.error {
    background-color: #fee2e2;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #dc2626;
    font-size: 0.875rem;
  }

  .enhanced-image-container.error::before {
    content: "Image failed to load";
  }

  /* Optimize for different contexts */
  .enhanced-image-container[data-context="product-grid"] {
    aspect-ratio: 4/3;
    max-width: 320px;
  }

  .enhanced-image-container[data-context="product-detail"] {
    aspect-ratio: 4/3;
    max-width: 600px;
  }

  .enhanced-image-container[data-context="hero"] {
    aspect-ratio: 16/9;
    max-width: 1200px;
  }

  /* Performance optimizations */
  .enhanced-image {
    will-change: opacity;
  }

  .image-placeholder {
    will-change: opacity;
  }

  /* Reduce layout shift */
  .enhanced-image-container {
    width: 100%;
    height: auto;
  }

  /* Responsive behavior */
  @media (max-width: 480px) {
    .enhanced-image-container[data-context="product-grid"] {
      max-width: 90vw;
    }
    
    .enhanced-image-container[data-context="product-detail"] {
      max-width: 95vw;
    }
  }

  @media (max-width: 768px) {
    .enhanced-image-container[data-context="product-grid"] {
      max-width: 45vw;
    }
    
    .enhanced-image-container[data-context="product-detail"] {
      max-width: 50vw;
    }
  }

  @media (max-width: 1200px) {
    .enhanced-image-container[data-context="product-grid"] {
      max-width: 30vw;
    }
  }
</style>

<script>
  // Enhanced lazy loading with intersection observer
  document.addEventListener('DOMContentLoaded', () => {
    const lazyImages = document.querySelectorAll('.lazy-image');
    
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const container = img.parentElement;
            
            img.onload = () => {
              container?.classList.add('loaded');
              img.classList.remove('lazy-image');
            };
            
            // Trigger load by setting opacity
            img.style.opacity = '1';
            observer.unobserve(img);
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.1
      });

      lazyImages.forEach(img => imageObserver.observe(img));
    } else {
      // Fallback for browsers without IntersectionObserver
      lazyImages.forEach(img => {
        const container = img.parentElement;
        img.onload = () => {
          container?.classList.add('loaded');
          img.classList.remove('lazy-image');
        };
        img.style.opacity = '1';
      });
    }
  });

  // Format detection and optimization
  (async function() {
    // Check for modern format support
    const supportsWebP = await new Promise<boolean>((resolve) => {
      const webP = new Image();
      webP.onload = webP.onerror = () => resolve(webP.height === 2);
      webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });

    const supportsAVIF = await new Promise<boolean>((resolve) => {
      const avif = new Image();
      avif.onload = avif.onerror = () => resolve(avif.height === 2);
      avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
    });

    // Store format support for future use
    if (supportsAVIF) {
      document.documentElement.classList.add('supports-avif');
    } else if (supportsWebP) {
      document.documentElement.classList.add('supports-webp');
    }
  })();
</script>
