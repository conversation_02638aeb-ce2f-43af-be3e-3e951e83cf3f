---
// Auto Breadcrumbs Component
// Automatically generates breadcrumbs based on the current URL and page context

import Breadcrumbs from './Breadcrumbs.astro';
import { generateBreadcrumbs, generateProductBreadcrumbs } from '../../utils/breadcrumbs.ts';
import type { BreadcrumbItem } from '../../utils/structuredData';

export interface Props {
  // Override automatic breadcrumb generation
  customItems?: BreadcrumbItem[];
  
  // Product-specific props (for product pages)
  productName?: string;
  productSlug?: string;
  productCategory?: string;
  
  // Page-specific props
  pageTitle?: string;
  category?: string;
  searchQuery?: string;
  
  // Breadcrumb display options
  showHome?: boolean;
  separator?: string;
  class?: string;
  ariaLabel?: string;
  
  // Control whether to show breadcrumbs at all
  show?: boolean;
}

const {
  customItems,
  productName,
  productSlug,
  productCategory,
  pageTitle,
  category,
  searchQuery,
  showHome = true,
  separator = '›',
  class: className = '',
  ariaLabel = 'Breadcrumb navigation',
  show = true
} = Astro.props;

let breadcrumbItems: BreadcrumbItem[] = [];

if (!show) {
  // Don't render breadcrumbs
} else if (customItems) {
  // Use custom items if provided
  breadcrumbItems = customItems;
} else {
  // Auto-generate breadcrumbs based on current URL and context
  const pathname = Astro.url.pathname;
  
  try {
    if (productName && productSlug) {
      // Product page breadcrumbs
      breadcrumbItems = await generateProductBreadcrumbs(
        productSlug,
        productName,
        productCategory
      );
    } else if (pathname.startsWith('/products') && category) {
      // Category page breadcrumbs
      const categorySlug = category.toLowerCase().replace(/[^a-z0-9]+/g, '-');
      breadcrumbItems = [
        { name: 'Home', url: '/', position: 1 },
        { name: 'Products', url: '/products/', position: 2 },
        {
          name: formatCategoryName(category),
          url: `/products/category/${categorySlug}/`,
          position: 3
        }
      ];
    } else if (pathname.startsWith('/products') && searchQuery) {
      // Search results breadcrumbs
      breadcrumbItems = [
        { name: 'Home', url: '/', position: 1 },
        { name: 'Products', url: '/products/', position: 2 },
        {
          name: `Search: "${searchQuery}"`,
          url: `/products/?search=${encodeURIComponent(searchQuery)}`,
          position: 3
        }
      ];
    } else {
      // Generate breadcrumbs from URL path
      breadcrumbItems = await generateBreadcrumbs(pathname, {
        baseUrl: Astro.site?.toString() || 'https://www.cheersmarketplace.com'
      });
    }
  } catch (error) {
    console.warn('Failed to generate breadcrumbs:', error);
    // Fallback to basic breadcrumbs
    breadcrumbItems = generateFallbackBreadcrumbs(pathname, pageTitle);
  }
}

// Helper function to format category names
function formatCategoryName(category: string): string {
  const categoryNames: Record<string, string> = {
    'electronics': 'Electronics',
    'clothing': 'Clothing & Fashion',
    'home-garden': 'Home & Garden',
    'books': 'Books',
    'toys-games': 'Toys & Games',
    'sports-outdoors': 'Sports & Outdoors',
    'health-beauty': 'Health & Beauty',
    'automotive': 'Automotive'
  };
  
  return categoryNames[category] || category
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Fallback breadcrumb generation
function generateFallbackBreadcrumbs(pathname: string, pageTitle?: string): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    { name: 'Home', url: '/', position: 1 }
  ];
  
  const segments = pathname.split('/').filter(segment => segment.length > 0);
  
  if (segments.length === 0) {
    return []; // Home page doesn't need breadcrumbs
  }
  
  let currentPath = '';
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === segments.length - 1;
    
    let name = segment;
    
    // Format common segments
    switch (segment) {
      case 'products':
        name = 'Products';
        break;
      case 'about':
        name = 'About';
        break;
      case 'faq':
        name = 'FAQ';
        break;
      case 'terms':
        name = 'Terms & Conditions';
        break;
      case 'privacy':
        name = 'Privacy Policy';
        break;
      default:
        if (isLast && pageTitle) {
          name = pageTitle;
        } else {
          name = segment
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
        }
        break;
    }
    
    items.push({
      name,
      url: currentPath,
      position: items.length + 1
    });
  });
  
  return items;
}

// Only show breadcrumbs if we have items and it's not the home page
const shouldShowBreadcrumbs = show && breadcrumbItems.length > 1 && Astro.url.pathname !== '/';
---

{shouldShowBreadcrumbs && (
  <Breadcrumbs 
    items={breadcrumbItems}
    showHome={showHome}
    separator={separator}
    class={className}
    ariaLabel={ariaLabel}
  />
)}

<!-- 
Usage Examples:

1. Auto-generated breadcrumbs (most common):
<AutoBreadcrumbs />

2. Product page:
<AutoBreadcrumbs 
  productName="Organic Cotton T-Shirt"
  productSlug="organic-cotton-t-shirt"
  productCategory="clothing"
/>

3. Category page:
<AutoBreadcrumbs 
  category="electronics"
  pageTitle="Electronics"
/>

4. Search results:
<AutoBreadcrumbs 
  searchQuery="vintage camera"
  pageTitle="Search Results"
/>

5. Custom breadcrumbs:
<AutoBreadcrumbs 
  customItems={[
    { name: "Home", url: "/", position: 1 },
    { name: "Custom Page", url: "/custom", position: 2 }
  ]}
/>

6. Hide breadcrumbs:
<AutoBreadcrumbs show={false} />

7. Custom styling:
<AutoBreadcrumbs 
  separator=">"
  class="my-breadcrumbs"
  ariaLabel="Custom navigation"
/>
-->
