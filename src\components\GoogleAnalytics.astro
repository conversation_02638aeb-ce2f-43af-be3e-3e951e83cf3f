---
// Google Analytics 4 component with performance optimizations
export interface Props {
  measurementId: string;
}

const { measurementId } = Astro.props;

// Only load GA in production
const isProduction = import.meta.env.PROD;
---

{isProduction && (
  <>
    <!-- DNS prefetch only for better performance -->
    <link rel="dns-prefetch" href="https://www.googletagmanager.com" />

    <!--
      Ultra-optimized Google Analytics loading:
      1. Load only on user interaction or after significant delay
      2. Minimal gtag configuration
      3. No Google Tag Manager overhead
    -->
    <script define:vars={{ measurementId }} is:inline>
      let gaLoaded = false;

      // Ultra-minimal GA loading
      function loadGA() {
        if (gaLoaded) return;
        gaLoaded = true;

        // Minimal gtag implementation
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        window.gtag = gtag;
        gtag('js', new Date());

        // Load script with minimal config
        const script = document.createElement('script');
        script.async = true;
        script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
        script.onload = () => {
          gtag('config', measurementId, {
            send_page_view: true,
            anonymize_ip: true,
            allow_google_signals: false,
            allow_ad_personalization_signals: false,
            cookie_flags: 'max-age=7200;secure;samesite=none'
          });
        };
        document.head.appendChild(script);
      }

      // Load on user interaction or after long delay
      let interacted = false;

      function handleInteraction() {
        if (interacted) return;
        interacted = true;

        if ('requestIdleCallback' in window) {
          requestIdleCallback(loadGA, { timeout: 1000 });
        } else {
          setTimeout(loadGA, 500);
        }
      }

      // Load on first meaningful interaction
      ['click', 'scroll', 'keydown', 'touchstart'].forEach(event => {
        document.addEventListener(event, handleInteraction, { once: true, passive: true });
      });

      // Fallback: load after 10 seconds if no interaction
      setTimeout(() => {
        if (!interacted) {
          handleInteraction();
        }
      }, 10000);
    </script>
  </>
)}

<!-- Development mode indicator -->
{!isProduction && (
  <script>
    console.log('🔍 Google Analytics disabled in development mode');
  </script>
)}
