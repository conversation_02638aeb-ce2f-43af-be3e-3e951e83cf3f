---
// Toast notification component for cart feedback
---

<!-- Toast Container -->
<div id="toast-container" class="toast-container"></div>

<style>
  .toast-container {
    position: fixed;
    top: 80px; /* Move below header */
    right: 20px;
    z-index: 9998; /* Below Snipcart modal but above everything else */
    pointer-events: none;
  }

  .toast {
    background: white;
    color: var(--text);
    padding: 1rem 1.25rem;
    border-radius: 12px;
    margin-bottom: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-light);
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 320px;
    max-width: 380px;
    backdrop-filter: blur(10px);
  }

  .toast.show {
    transform: translateX(0);
  }

  .toast.success {
    border-left: 4px solid #10b981;
  }

  .toast.success .toast-icon {
    color: #10b981;
  }

  .toast.error {
    border-left: 4px solid #ef4444;
  }

  .toast.error .toast-icon {
    color: #ef4444;
  }

  .toast.warning {
    border-left: 4px solid #f59e0b;
  }

  .toast.warning .toast-icon {
    color: #f59e0b;
  }

  .toast-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
  }

  .toast-content {
    flex: 1;
  }

  .toast-title {
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: var(--text);
  }

  .toast-message {
    margin: 0;
    font-size: 0.8125rem;
    color: var(--text-secondary);
    line-height: 1.4;
  }



  @media (max-width: 480px) {
    .toast-container {
      top: 70px;
      right: 15px;
      left: 15px;
    }

    .toast {
      min-width: auto;
      max-width: none;
      padding: 0.875rem 1rem;
      font-size: 0.875rem;
    }

    .toast-title {
      font-size: 0.8125rem;
    }

    .toast-message {
      font-size: 0.75rem;
    }
  }

  /* Smooth entrance animation */
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  .toast.show {
    animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .toast.hide {
    animation: slideOutRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
</style>

<!-- Toast notifications functionality is now included in critical.bundle.js -->
<!-- No additional script loading needed -->
