---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import '../assets/global.css';
---

<Layout
  title="Terms & Conditions | Cheers Marketplace"
  description="Read the terms and conditions for shopping at Cheers Marketplace, including our policies and guidelines."
>
    <meta name="description" content="Read the terms and conditions for using Cheers Marketplace, including user agreements, payment terms, and policies." />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="Terms & Conditions | Cheers Marketplace" />
    <meta property="og:description" content="Read the terms and conditions for using Cheers Marketplace, including user agreements, payment terms, and policies." />
    
    <!-- Twitter -->
    <meta name="twitter:title" content="Terms & Conditions | Cheers Marketplace" />
    <meta name="twitter:description" content="Read the terms and conditions for using Cheers Marketplace, including user agreements, payment terms, and policies." />
  </Fragment>

  <Header />

  <main class="terms-main">
    <!-- Hero Section -->
    <section class="terms-hero">
      <div class="container">
        <h1>Terms & Conditions</h1>
        <p class="hero-description">
          Please read these terms carefully before using Cheers Marketplace
        </p>
        <p class="last-updated">Last updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
      </div>
    </section>

    <!-- Terms Content -->
    <section class="terms-content">
      <div class="container">
        <div class="terms-document">
          
          <div class="terms-section">
            <h2>1. Acceptance of Terms</h2>
            <p>By accessing and using Cheers Marketplace ("we," "us," or "our"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.</p>
          </div>

          <div class="terms-section">
            <h2>2. Use License</h2>
            <p>Permission is granted to temporarily download one copy of the materials on Cheers Marketplace for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:</p>
            <ul>
              <li>modify or copy the materials</li>
              <li>use the materials for any commercial purpose or for any public display</li>
              <li>attempt to reverse engineer any software contained on the website</li>
              <li>remove any copyright or other proprietary notations from the materials</li>
            </ul>
          </div>

          <div class="terms-section">
            <h2>3. Product Information and Pricing</h2>
            <p>We strive to provide accurate product descriptions and pricing information. However, we do not warrant that product descriptions or other content is accurate, complete, reliable, current, or error-free.</p>
            <p>Prices for our products are subject to change without notice. We reserve the right at any time to modify or discontinue any product without notice.</p>
          </div>

          <div class="terms-section">
            <h2>4. Orders and Payment</h2>
            <p>By placing an order, you represent that you are at least 18 years old and legally capable of entering into binding contracts.</p>
            <p>We reserve the right to refuse or cancel any order for any reason, including but not limited to product availability, errors in product or pricing information, or suspected fraudulent activity.</p>
            <p>Payment must be received by us before we ship any products. We accept major credit cards, PayPal, and other payment methods as displayed during checkout.</p>
          </div>

          <div class="terms-section">
            <h2>5. Shipping and Delivery</h2>
            <p>We will make every effort to ship products within the timeframes specified on our website. However, delivery dates are estimates and we are not responsible for delays caused by shipping carriers or other circumstances beyond our control.</p>
            <p>Risk of loss and title for products pass to you upon delivery to the shipping carrier.</p>
          </div>

          <div class="terms-section">
            <h2>6. Returns and Refunds</h2>
            <p>We offer a 30-day return policy for most products. Items must be returned in their original condition and packaging. Some items, such as personalized products, may not be eligible for return.</p>
            <p>Refunds will be processed to the original payment method within 5-10 business days after we receive the returned item.</p>
            <p>Customers are responsible for return shipping costs unless the return is due to our error.</p>
          </div>

          <div class="terms-section">
            <h2>7. User Accounts</h2>
            <p>When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account.</p>
            <p>We reserve the right to terminate accounts that violate these terms or engage in fraudulent or harmful activities.</p>
          </div>

          <div class="terms-section">
            <h2>8. Intellectual Property</h2>
            <p>The service and its original content, features, and functionality are and will remain the exclusive property of Cheers Marketplace and its licensors. The service is protected by copyright, trademark, and other laws.</p>
          </div>

          <div class="terms-section">
            <h2>9. Limitation of Liability</h2>
            <p>In no event shall Cheers Marketplace, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of the service.</p>
          </div>

          <div class="terms-section">
            <h2>10. Disclaimer</h2>
            <p>The information on this website is provided on an "as is" basis. To the fullest extent permitted by law, this Company excludes all representations, warranties, conditions and terms relating to our website and the use of this website.</p>
          </div>

          <div class="terms-section">
            <h2>11. Governing Law</h2>
            <p>These terms and conditions are governed by and construed in accordance with the laws of the jurisdiction in which Cheers Marketplace operates, and you irrevocably submit to the exclusive jurisdiction of the courts in that state or location.</p>
          </div>

          <div class="terms-section">
            <h2>12. Changes to Terms</h2>
            <p>We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.</p>
            <p>Your continued use of the service after any such changes constitutes your acceptance of the new Terms.</p>
          </div>

          <div class="terms-section">
            <h2>13. Contact Information</h2>
            <p>If you have any questions about these Terms & Conditions, please contact us at:</p>
            <p>
              Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
              Address: Cheers Marketplace Legal Department
            </p>
          </div>

        </div>
      </div>
    </section>
  </main>

  <Footer />
</Layout>

<style>
  .terms-main {
    padding-top: 0;
  }

  .terms-hero {
    background: linear-gradient(135deg, var(--light-background) 0%, var(--border-light) 100%);
    border-bottom: 1px solid var(--border);
    padding: 2rem 0;
    text-align: center;
  }

  .terms-hero h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text);
  }

  .hero-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
  }

  .last-updated {
    font-size: 0.875rem;
    color: var(--muted);
    font-style: italic;
  }

  .terms-content {
    padding: 3rem 0;
  }

  .terms-document {
    max-width: 800px;
    margin: 0 auto;
    background: var(--card-bg);
    border-radius: var(--radius-lg);
    padding: 3rem;
    border: 1px solid var(--border);
    box-shadow: var(--shadow-sm);
  }

  .terms-section {
    margin-bottom: 2.5rem;
  }

  .terms-section:last-child {
    margin-bottom: 0;
  }

  .terms-section h2 {
    font-size: 1.375rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text);
    border-bottom: 1px solid var(--border);
    padding-bottom: 0.5rem;
  }

  .terms-section p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1rem;
  }

  .terms-section p:last-child {
    margin-bottom: 0;
  }

  .terms-section ul {
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 1rem 0;
    padding-left: 1.5rem;
  }

  .terms-section li {
    margin-bottom: 0.5rem;
  }

  .terms-section a {
    color: var(--primary);
    text-decoration: none;
  }

  .terms-section a:hover {
    text-decoration: underline;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .terms-hero h1 {
      font-size: 2rem;
    }

    .hero-description {
      font-size: 1rem;
    }

    .terms-content {
      padding: 2rem 0;
    }

    .terms-document {
      padding: 2rem;
    }

    .terms-section h2 {
      font-size: 1.25rem;
    }
  }

  @media (max-width: 480px) {
    .terms-hero {
      padding: 1.5rem 0;
    }

    .terms-hero h1 {
      font-size: 1.75rem;
    }

    .terms-document {
      padding: 1.5rem;
    }

    .terms-section h2 {
      font-size: 1.125rem;
    }
  }
</style>
