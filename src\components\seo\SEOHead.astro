---
// SEO Head Component
// Comprehensive SEO meta tags with dynamic descriptions

export interface Props {
  title: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
  canonical?: string;
  type?: 'website' | 'article' | 'product';
  // Product-specific props
  product?: {
    name: string;
    category: string;
    price: number;
    shortDescription?: string;
    description?: string;
    condition?: string;
    brand?: string;
  };
  // Page-specific props
  pageType?: 'homepage' | 'products' | 'product' | 'about' | 'faq' | 'terms' | 'privacy' | 'category' | 'search';
  category?: string;
  searchQuery?: string;
  totalProducts?: number;
}

import {
  generateHomepageDescription,
  generateProductDescription,
  generateProductsListingDescription,
  generateAboutDescription,
  generateFAQDescription,
  generateTermsDescription,
  generatePrivacyDescription,
  generateCategoryDescription,
  generateSearchDescription,
  generateFallbackDescription
} from '../../utils/metaDescriptions.ts';

const {
  title,
  description,
  image = "/cheers-marketplace-og.jpg",
  noIndex = false,
  canonical,
  type = 'website',
  product,
  pageType,
  category,
  searchQuery,
  totalProducts
} = Astro.props;

// Generate dynamic description if not provided
let finalDescription = description;

if (!finalDescription && pageType) {
  switch (pageType) {
    case 'homepage':
      finalDescription = generateHomepageDescription();
      break;
    case 'product':
      if (product) {
        finalDescription = generateProductDescription(product);
      }
      break;
    case 'products':
      finalDescription = generateProductsListingDescription(category, searchQuery, totalProducts);
      break;
    case 'about':
      finalDescription = generateAboutDescription();
      break;
    case 'faq':
      finalDescription = generateFAQDescription();
      break;
    case 'terms':
      finalDescription = generateTermsDescription();
      break;
    case 'privacy':
      finalDescription = generatePrivacyDescription();
      break;
    case 'category':
      if (category) {
        finalDescription = generateCategoryDescription(category, totalProducts);
      }
      break;
    case 'search':
      if (searchQuery) {
        finalDescription = generateSearchDescription(searchQuery, totalProducts);
      }
      break;
  }
}

// Fallback description
if (!finalDescription) {
  finalDescription = generateFallbackDescription(title);
}

// Canonical URL - ensure consistent format with trailing slash to match Astro config
const baseCanonicalURL = canonical || new URL(Astro.url.pathname, Astro.site);
// Ensure trailing slash for consistency with trailingSlash: 'always' config
let canonicalURL: string;
if (typeof baseCanonicalURL === 'string') {
  canonicalURL = baseCanonicalURL.endsWith('/') || baseCanonicalURL.includes('.') ? baseCanonicalURL : baseCanonicalURL + '/';
} else {
  if (!baseCanonicalURL.pathname.endsWith('/') && !baseCanonicalURL.pathname.includes('.')) {
    baseCanonicalURL.pathname += '/';
  }
  canonicalURL = baseCanonicalURL.toString();
}

// Open Graph image URL
const ogImage = new URL(image, Astro.site);

// Structured title for better SEO
const structuredTitle = title.includes('Cheers Marketplace') 
  ? title 
  : `${title} | Cheers Marketplace`;
---

<!-- Primary Meta Tags -->
<title>{structuredTitle}</title>
<meta name="title" content={structuredTitle} />
<meta name="description" content={finalDescription} />
<link rel="canonical" href={canonicalURL} />

<!-- Robots -->
{noIndex ? (
  <meta name="robots" content="noindex, nofollow" />
) : (
  <meta name="robots" content="index, follow" />
)}

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:title" content={structuredTitle} />
<meta property="og:description" content={finalDescription} />
<meta property="og:image" content={ogImage} />
<meta property="og:site_name" content="Cheers Marketplace" />
<meta property="og:locale" content="en_US" />

<!-- Twitter -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:url" content={canonicalURL} />
<meta name="twitter:title" content={structuredTitle} />
<meta name="twitter:description" content={finalDescription} />
<meta name="twitter:image" content={ogImage} />
<meta name="twitter:site" content="@cheersmarket" />
<meta name="twitter:creator" content="@cheersmarket" />

<!-- Additional SEO Meta Tags -->
<meta name="author" content="Cheers Marketplace" />
<meta name="publisher" content="Cheers Marketplace" />
<meta name="copyright" content="Cheers Marketplace" />
<meta name="language" content="English" />
<meta name="revisit-after" content="7 days" />
<meta name="distribution" content="web" />
<meta name="rating" content="general" />

<!-- Geographic Meta Tags -->
<meta name="geo.region" content="US-CA" />
<meta name="geo.placename" content="Chico, California" />
<meta name="geo.position" content="39.7285;-121.8375" />
<meta name="ICBM" content="39.7285, -121.8375" />

<!-- Product-specific meta tags -->
{product && (
  <>
    <meta property="product:price:amount" content={product.price.toString()} />
    <meta property="product:price:currency" content="USD" />
    <meta property="product:availability" content="in stock" />
    <meta property="product:condition" content={product.condition || "used"} />
    <meta property="product:category" content={product.category} />
    {product.brand && <meta property="product:brand" content={product.brand} />}
  </>
)}

<!-- Article-specific meta tags for blog-like content -->
{type === 'article' && (
  <>
    <meta property="article:author" content="Cheers Marketplace" />
    <meta property="article:publisher" content="Cheers Marketplace" />
    <meta property="article:section" content={category || "General"} />
  </>
)}

<!-- Schema.org meta tags -->
<meta itemprop="name" content={structuredTitle} />
<meta itemprop="description" content={finalDescription} />
<meta itemprop="image" content={ogImage} />

<!-- Mobile and viewport -->
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta name="format-detection" content="telephone=no" />

<!-- Theme and appearance -->
<meta name="theme-color" content="#92400e" />
<meta name="msapplication-TileColor" content="#92400e" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />

<!-- Preconnect to external domains for performance -->
<link rel="preconnect" href="https://cdn.snipcart.com" crossorigin />
<link rel="preconnect" href="https://www.googletagmanager.com" />
<link rel="preconnect" href="https://images.unsplash.com" />

<!-- DNS prefetch for additional performance -->
<link rel="dns-prefetch" href="//fonts.googleapis.com" />
<link rel="dns-prefetch" href="//www.google-analytics.com" />

<!-- 
Usage Examples:

1. Homepage:
<SEOHead 
  title="Cheers Marketplace - Quality Secondhand Goods in Chico, CA"
  pageType="homepage"
/>

2. Product page:
<SEOHead 
  title={product.name}
  pageType="product"
  product={product}
  type="product"
/>

3. Category page:
<SEOHead 
  title="Electronics - Cheers Marketplace"
  pageType="category"
  category="electronics"
  totalProducts={42}
/>

4. Search results:
<SEOHead 
  title="Search Results"
  pageType="search"
  searchQuery="vintage camera"
  totalProducts={5}
/>

5. Custom description:
<SEOHead 
  title="About Us"
  description="Custom description that overrides auto-generation"
  pageType="about"
/>
-->
