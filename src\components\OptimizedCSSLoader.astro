---
/**
 * Optimized CSS Loader Component
 * Implements advanced CSS loading strategies to reduce render-blocking CSS
 * and improve Core Web Vitals
 */

export interface Props {
  criticalCSS?: string;
  nonCriticalCSS?: string[];
  enablePreload?: boolean;
  enablePrefetch?: boolean;
}

const {
  criticalCSS,
  nonCriticalCSS = [],
  enablePreload = true,
  enablePrefetch = true
} = Astro.props;

// Define critical CSS that must be inlined
const inlineCriticalCSS = `
/* Ultra-minimal critical CSS for instant rendering */
:root{--primary:#92400e;--primary-dark:#78350f;--primary-light:#d97706;--secondary:#1e293b;--background:#f8fafc;--light-background:#ffffff;--card-bg:#ffffff;--text:#0f172a;--text-secondary:#475569;--border:#e2e8f0;--radius:0.5rem;--radius-lg:0.75rem;--radius-xl:1rem;--container-width:1200px;--shadow:0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);--shadow-lg:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--font-system:system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;--font-serif:Georgia,"Times New Roman",serif}
*,*::before,*::after{box-sizing:border-box}
html,body{margin:0;padding:0;font-family:var(--font-system);background:var(--background);color:var(--text);line-height:1.6;font-size:16px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
.container{max-width:var(--container-width);margin:0 auto;padding:0 2rem}
h1,h2,h3{font-family:var(--font-serif);color:var(--text);font-weight:600;line-height:1.2;margin:0 0 1rem 0}
h1{font-size:2.5rem}h2{font-size:2rem}h3{font-size:1.5rem}
.site-header{background:var(--light-background);border-bottom:1px solid var(--border);position:sticky;top:0;z-index:100;backdrop-filter:blur(10px)}
.header-flex{display:flex;align-items:center;justify-content:space-between;padding:1rem 2rem;min-height:70px}
.logo{display:flex;align-items:center;text-decoration:none;transition:opacity 0.2s ease}
.main-nav{display:flex;gap:3rem;align-items:center;flex:1;justify-content:center;margin:0 2rem}
.main-nav a{text-decoration:none;color:var(--text-secondary);font-weight:500;transition:color 0.2s ease;padding:0.75rem 1rem;border-radius:var(--radius);white-space:nowrap;font-size:0.95rem}
.main-nav a:hover{color:var(--primary)}
.header-actions{display:flex;align-items:center;gap:1rem}
.cart-trigger{background:none;border:none;color:var(--text);cursor:pointer;padding:0.5rem;border-radius:var(--radius);transition:all 0.2s ease;position:relative}
.cart-trigger:hover{background:rgba(146,64,14,0.1);color:var(--primary)}
.cart-count{position:absolute;top:-8px;right:-8px;background:var(--primary);color:white;border-radius:50%;width:20px;height:20px;font-size:0.75rem;display:flex;align-items:center;justify-content:center;font-weight:600}
.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}
*:focus-visible{outline:2px solid var(--primary);outline-offset:2px}
@media (min-width: 769px){.main-nav{display:flex !important;gap:3rem;align-items:center;flex:1;justify-content:center;margin:0 2rem}}
@media (max-width: 768px){.main-nav{display:none}.container{padding:0 1rem}.header-flex{padding:1rem}h1{font-size:2rem}h2{font-size:1.75rem}h3{font-size:1.25rem}}
@media (prefers-reduced-motion: reduce){*,*::before,*::after{animation-duration:0.01ms !important;animation-iteration-count:1 !important;transition-duration:0.01ms !important;scroll-behavior:auto !important}}
`;

// Define non-critical CSS files to load asynchronously (only existing files)
// Note: In development, these files may not exist yet, so we'll handle 404s gracefully
const defaultNonCriticalCSS: string[] = [];

const cssFilesToLoad = nonCriticalCSS.length > 0 ? nonCriticalCSS : defaultNonCriticalCSS;
---

<!-- Inline Critical CSS for instant rendering -->
<style>
  {inlineCriticalCSS}
  {criticalCSS && criticalCSS}
</style>

<!-- Preload disabled to prevent 404 errors -->
{false && enablePreload && cssFilesToLoad.map(cssFile => (
  <link rel="preload" href={cssFile} as="style" onload="this.onload=null;this.rel='stylesheet'" />
))}

<!-- Prefetch disabled temporarily to avoid 404 errors -->
{false && enablePrefetch && (
  <>
    <link rel="prefetch" href="/assets/css/products.css" />
    <link rel="prefetch" href="/assets/css/header.css" />
  </>
)}

<!-- Advanced CSS Loading Script -->
<script is:inline>
  (function() {
    'use strict';
    
    // CSS loading utilities
    const CSSLoader = {
      loadedCSS: new Set(),
      
      // Load CSS file asynchronously
      loadCSS: function(href, media = 'all') {
        if (this.loadedCSS.has(href)) return Promise.resolve();
        
        return new Promise((resolve, reject) => {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = href;
          link.media = media;
          
          link.onload = () => {
            this.loadedCSS.add(href);
            resolve();
          };
          
          link.onerror = () => {
            console.warn(`Failed to load CSS: ${href}`);
            reject(new Error(`Failed to load CSS: ${href}`));
          };
          
          document.head.appendChild(link);
        });
      },
      
      // Load CSS with print media trick for non-blocking
      loadCSSNonBlocking: function(href) {
        if (this.loadedCSS.has(href)) return Promise.resolve();
        
        return new Promise((resolve) => {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = href;
          link.media = 'print';
          
          link.onload = () => {
            link.media = 'all';
            this.loadedCSS.add(href);
            resolve();
          };
          
          link.onerror = () => {
            console.warn(`Failed to load CSS: ${href}`);
            resolve(); // Don't reject to prevent blocking other CSS
          };
          
          document.head.appendChild(link);
        });
      },
      
      // Load multiple CSS files
      loadMultipleCSS: function(hrefs, nonBlocking = true) {
        const loadMethod = nonBlocking ? this.loadCSSNonBlocking : this.loadCSS;
        return Promise.all(hrefs.map(href => loadMethod.call(this, href)));
      },
      
      // Load CSS based on media query
      loadCSSConditional: function(href, mediaQuery) {
        if (!window.matchMedia(mediaQuery).matches) return Promise.resolve();
        return this.loadCSS(href);
      }
    };
    
    // Load non-critical CSS after page load
    function loadNonCriticalCSS() {
      // Temporarily disabled to avoid 404 errors in development
      const nonCriticalFiles = [];
      
      CSSLoader.loadMultipleCSS(nonCriticalFiles, true).then(() => {
        console.log('Non-critical CSS loaded');
        document.documentElement.classList.add('css-loaded');
      });
    }
    
    // Load conditional CSS based on features
    function loadConditionalCSS() {
      // Temporarily disabled to avoid 404 errors in development
      // CSS files will be loaded through Astro's built-in system instead
    }
    
    // Optimize CSS loading based on connection speed
    function optimizeForConnection() {
      if ('connection' in navigator) {
        const connection = navigator.connection;
        
        // Load fewer resources on slow connections
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
          console.log('Slow connection detected, skipping non-essential CSS');
          return;
        }
        
        // Preload disabled to prevent 404 errors
        // CSS is handled through Astro's built-in system
      }
    }
    
    // Initialize CSS loading
    function initCSSLoading() {
      // Load non-critical CSS after initial render
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadNonCriticalCSS);
      } else {
        loadNonCriticalCSS();
      }
      
      // Load conditional CSS
      loadConditionalCSS();
      
      // Optimize for connection
      optimizeForConnection();
      
      // Listen for route changes to load page-specific CSS
      window.addEventListener('popstate', () => {
        loadConditionalCSS();
      });
    }
    
    // Start CSS loading optimization
    initCSSLoading();
    
    // Expose CSS loader for manual use
    window.CSSLoader = CSSLoader;
  })();
</script>

<!-- Fallback for browsers without JavaScript -->
<noscript>
  {cssFilesToLoad.map(cssFile => (
    <link rel="stylesheet" href={cssFile} />
  ))}
</noscript>

<style>
  /* Loading state optimization */
  html:not(.css-loaded) {
    /* Minimal styles while CSS loads */
  }
  
  html.css-loaded {
    /* Enhanced styles after CSS loads */
  }
  
  /* Prevent FOUC (Flash of Unstyled Content) */
  .css-loading {
    visibility: hidden;
  }
  
  html.css-loaded .css-loading {
    visibility: visible;
    animation: fadeIn 0.3s ease-in-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  /* Performance optimizations */
  @media (prefers-reduced-motion: reduce) {
    .css-loading {
      animation: none !important;
    }
  }
</style>
