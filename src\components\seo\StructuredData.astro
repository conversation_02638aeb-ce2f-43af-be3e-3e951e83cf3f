---
// Base Structured Data Component
// Renders JSON-LD structured data for SEO

export interface Props {
  schema: object | object[];
  id?: string;
}

const { schema, id } = Astro.props;

// Convert schema to JSON-LD string
function schemaToJsonLd(data: object | object[]): string {
  if (Array.isArray(data)) {
    if (data.length === 1) {
      return JSON.stringify(data[0], null, 0);
    }
    return JSON.stringify({
      "@context": "https://schema.org",
      "@graph": data
    }, null, 0);
  }
  return JSON.stringify(data, null, 0);
}

const jsonLd = schemaToJsonLd(schema);
---

<script type="application/ld+json" set:html={jsonLd} {id}></script>

<!-- 
Usage Examples:

1. Single schema:
<StructuredData schema={organizationSchema} />

2. Multiple schemas:
<StructuredData schema={[organizationSchema, webSiteSchema]} />

3. With ID for debugging:
<StructuredData schema={productSchema} id="product-schema" />
-->
