/* Prevent layout shift from scrollbar appearing on long pages */
html {
  overflow-y: scroll;
}
/* Optimized Global CSS - Critical Path Only */
/* Non-critical styles are loaded asynchronously via Layout.astro */

/* Import only critical CSS modules for above-the-fold content */
@import './css/variables.css';
@import './css/base.css';
@import './css/header.css';

/* Homepage specific styles */
.homepage-card {
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: 3rem 2.5rem;
  margin: 2rem auto;
  max-width: 640px;
  width: 100%;
  text-align: center;
  border: 1px solid var(--border);
}

/* Page-specific styles that don't warrant separate files */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text);
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
