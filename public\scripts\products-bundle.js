// Products Page JavaScript Bundle
// Contains filtering, search, and product-specific functionality

(function() {
  'use strict';

  // Product Filtering System
  const ProductFilter = {
    products: [],
    filteredProducts: [],
    currentFilters: {
      category: '',
      search: '',
      minPrice: 0,
      maxPrice: Infinity,
      sortBy: 'name',
      sortOrder: 'asc'
    },

    init() {
      this.cacheProducts();
      this.bindEvents();
      this.initializeFilters();
    },

    cacheProducts() {
      const productCards = document.querySelectorAll('.product-card');
      this.products = Array.from(productCards).map(card => ({
        element: card,
        name: card.dataset.name || '',
        category: card.dataset.category || '',
        description: card.dataset.description || '',
        price: parseFloat(card.dataset.price) || 0,
        index: parseInt(card.dataset.index) || 0
      }));
      this.filteredProducts = [...this.products];
    },

    bindEvents() {
      // Search input
      const searchInput = document.getElementById('product-search');
      if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
          clearTimeout(searchTimeout);
          searchTimeout = setTimeout(() => {
            this.updateFilter('search', e.target.value.toLowerCase());
          }, 300);
        });
      }

      // Category filter
      const categorySelect = document.getElementById('category-filter');
      if (categorySelect) {
        categorySelect.addEventListener('change', (e) => {
          this.updateFilter('category', e.target.value);
        });
      }

      // Price range filters
      const minPriceInput = document.getElementById('min-price');
      const maxPriceInput = document.getElementById('max-price');
      
      if (minPriceInput) {
        minPriceInput.addEventListener('change', (e) => {
          this.updateFilter('minPrice', parseFloat(e.target.value) || 0);
        });
      }
      
      if (maxPriceInput) {
        maxPriceInput.addEventListener('change', (e) => {
          this.updateFilter('maxPrice', parseFloat(e.target.value) || Infinity);
        });
      }

      // Sort controls
      const sortSelect = document.getElementById('sort-select');
      if (sortSelect) {
        sortSelect.addEventListener('change', (e) => {
          const [sortBy, sortOrder] = e.target.value.split('-');
          this.updateFilter('sortBy', sortBy);
          this.updateFilter('sortOrder', sortOrder);
        });
      }

      // Clear filters button
      const clearButton = document.getElementById('clear-filters');
      if (clearButton) {
        clearButton.addEventListener('click', () => {
          this.clearAllFilters();
        });
      }
    },

    updateFilter(key, value) {
      this.currentFilters[key] = value;
      this.applyFilters();
    },

    applyFilters() {
      const { category, search, minPrice, maxPrice, sortBy, sortOrder } = this.currentFilters;

      // Filter products
      this.filteredProducts = this.products.filter(product => {
        // Category filter
        if (category && product.category !== category) return false;
        
        // Search filter
        if (search) {
          const searchText = `${product.name} ${product.description} ${product.category}`.toLowerCase();
          if (!searchText.includes(search)) return false;
        }
        
        // Price range filter
        if (product.price < minPrice || product.price > maxPrice) return false;
        
        return true;
      });

      // Sort products
      this.filteredProducts.sort((a, b) => {
        let comparison = 0;
        
        switch (sortBy) {
          case 'name':
            comparison = a.name.localeCompare(b.name);
            break;
          case 'price':
            comparison = a.price - b.price;
            break;
          case 'newest':
            comparison = b.index - a.index; // Higher index = newer
            break;
          case 'oldest':
            comparison = a.index - b.index; // Lower index = older
            break;
        }
        
        return sortOrder === 'desc' ? -comparison : comparison;
      });

      this.updateDisplay();
      this.updateResultsCount();
    },

    updateDisplay() {
      // Hide all products first
      this.products.forEach(product => {
        product.element.style.display = 'none';
        product.element.classList.remove('filtered-in');
      });

      // Show filtered products with animation
      this.filteredProducts.forEach((product, index) => {
        product.element.style.display = 'block';
        setTimeout(() => {
          product.element.classList.add('filtered-in');
        }, index * 50); // Stagger animation
      });

      // Update empty state
      this.updateEmptyState();
    },

    updateResultsCount() {
      const countElement = document.getElementById('results-count');
      if (countElement) {
        const count = this.filteredProducts.length;
        const total = this.products.length;
        countElement.textContent = count === total 
          ? `${count} products` 
          : `${count} of ${total} products`;
      }
    },

    updateEmptyState() {
      const container = document.querySelector('.products-grid');
      let emptyState = document.querySelector('.empty-state');
      
      if (this.filteredProducts.length === 0) {
        if (!emptyState) {
          emptyState = document.createElement('div');
          emptyState.className = 'empty-state';
          emptyState.innerHTML = `
            <div class="empty-state-content">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              <h3>No products found</h3>
              <p>Try adjusting your filters or search terms</p>
              <button id="clear-filters-empty" class="btn primary">Clear Filters</button>
            </div>
          `;
          container.appendChild(emptyState);
          
          // Bind clear filters button in empty state
          emptyState.querySelector('#clear-filters-empty').addEventListener('click', () => {
            this.clearAllFilters();
          });
        }
        emptyState.style.display = 'flex';
      } else if (emptyState) {
        emptyState.style.display = 'none';
      }
    },

    clearAllFilters() {
      // Reset filters
      this.currentFilters = {
        category: '',
        search: '',
        minPrice: 0,
        maxPrice: Infinity,
        sortBy: 'name',
        sortOrder: 'asc'
      };

      // Reset form inputs
      const searchInput = document.getElementById('product-search');
      const categorySelect = document.getElementById('category-filter');
      const minPriceInput = document.getElementById('min-price');
      const maxPriceInput = document.getElementById('max-price');
      const sortSelect = document.getElementById('sort-select');

      if (searchInput) searchInput.value = '';
      if (categorySelect) categorySelect.value = '';
      if (minPriceInput) minPriceInput.value = '';
      if (maxPriceInput) maxPriceInput.value = '';
      if (sortSelect) sortSelect.value = 'name-asc';

      this.applyFilters();
    },

    initializeFilters() {
      // Apply any initial filters from URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      
      if (urlParams.has('category')) {
        this.updateFilter('category', urlParams.get('category'));
        const categorySelect = document.getElementById('category-filter');
        if (categorySelect) categorySelect.value = urlParams.get('category');
      }
      
      if (urlParams.has('search')) {
        this.updateFilter('search', urlParams.get('search').toLowerCase());
        const searchInput = document.getElementById('product-search');
        if (searchInput) searchInput.value = urlParams.get('search');
      }

      this.applyFilters();
    }
  };

  // Product Image Gallery (for product detail pages)
  const ProductGallery = {
    init() {
      this.bindThumbnailEvents();
    },

    bindThumbnailEvents() {
      const mainImage = document.getElementById('main-product-image');
      const thumbnails = document.querySelectorAll('.thumbnail');

      if (!mainImage || thumbnails.length === 0) return;

      thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
          const newImageSrc = this.dataset.image;
          if (newImageSrc) {
            // Update main image using the correct scope for optimizeImageUrl
            mainImage.src = ProductGallery.optimizeImageUrl(newImageSrc, 800, 600);
            mainImage.alt = this.querySelector('img').alt;

            // Update active thumbnail
            thumbnails.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
          }
        }, { passive: true });
      });
    },

    optimizeImageUrl(url, width = 800, height = 600) {
      if (!url) return '/images/product-placeholder.svg';

      if (url.includes('unsplash.com')) {
        const baseUrl = url.split('?')[0];
        return `${baseUrl}?w=${width}&h=${height}&auto=format&fit=crop&q=85&fm=webp`;
      }

      return url;
    }
  };

  // Initialize based on page type
  function initializeProducts() {
    // Check if we're on the products listing page
    if (document.querySelector('.products-grid')) {
      ProductFilter.init();
    }

    // Check if we're on a product detail page
    if (document.querySelector('.product-images')) {
      ProductGallery.init();
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeProducts);
  } else {
    initializeProducts();
  }

  // Expose utilities globally
  window.CheersMPProducts = {
    filter: ProductFilter,
    gallery: ProductGallery
  };

})();
