---
// Breadcrumb Schema Component
// Generates breadcrumb structured data for navigation

import StructuredData from './StructuredData.astro';
import { generateBreadcrumbSchema, type BreadcrumbItem } from '../../utils/structuredData.ts';

export interface Props {
  items: BreadcrumbItem[];
}

const { items } = Astro.props;

// Validate and prepare breadcrumb items
const validItems = items
  .filter(item => item.name && item.url)
  .map((item, index) => ({
    ...item,
    position: item.position || index + 1
  }));

const breadcrumbSchema = generateBreadcrumbSchema(validItems);
---

{validItems.length > 1 && (
  <StructuredData schema={breadcrumbSchema} id="breadcrumb-schema" />
)}

<!-- 
This component generates breadcrumb structured data for SEO.
Only renders if there are at least 2 breadcrumb items.

Usage:
<BreadcrumbSchema items={[
  { name: "Home", url: "/", position: 1 },
  { name: "Products", url: "/products", position: 2 },
  { name: "Electronics", url: "/products?category=electronics", position: 3 },
  { name: "Smartphone", url: "/products/smartphone", position: 4 }
]} />
-->
