/**
 * Cache Optimization Utilities
 * Provides tools for cache busting, versioning, and performance optimization
 */

export interface CacheConfig {
  version: string;
  enableVersioning: boolean;
  enableCacheBusting: boolean;
  staticAssetMaxAge: number;
  htmlMaxAge: number;
  apiMaxAge: number;
}

export interface AssetInfo {
  url: string;
  type: 'css' | 'js' | 'image' | 'font' | 'html' | 'api';
  size?: number;
  lastModified?: Date;
  hash?: string;
}

/**
 * Default cache configuration
 */
export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  version: '1.0.0',
  enableVersioning: true,
  enableCacheBusting: true,
  staticAssetMaxAge: 31536000, // 1 year
  htmlMaxAge: 3600, // 1 hour
  apiMaxAge: 300 // 5 minutes
};

/**
 * Generate cache-busting hash from content
 */
export function generateCacheHash(content: string): string {
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

/**
 * Add version parameter to URL
 */
export function addVersionToUrl(url: string, version: string): string {
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}v=${version}`;
}

/**
 * Add cache-busting parameter to URL
 */
export function addCacheBustingToUrl(url: string, hash?: string): string {
  const cacheBuster = hash || Date.now().toString(36);
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}cb=${cacheBuster}`;
}

/**
 * Optimize asset URL with versioning and cache busting
 */
export function optimizeAssetUrl(
  url: string, 
  config: Partial<CacheConfig> = {},
  content?: string
): string {
  const finalConfig = { ...DEFAULT_CACHE_CONFIG, ...config };
  let optimizedUrl = url;
  
  // Add versioning if enabled
  if (finalConfig.enableVersioning) {
    optimizedUrl = addVersionToUrl(optimizedUrl, finalConfig.version);
  }
  
  // Add cache busting if enabled and content is provided
  if (finalConfig.enableCacheBusting && content) {
    const hash = generateCacheHash(content);
    optimizedUrl = addCacheBustingToUrl(optimizedUrl, hash);
  }
  
  return optimizedUrl;
}

/**
 * Generate cache headers for different asset types
 */
export function generateCacheHeaders(assetType: AssetInfo['type'], config: Partial<CacheConfig> = {}): Record<string, string> {
  const finalConfig = { ...DEFAULT_CACHE_CONFIG, ...config };
  
  const baseHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'Vary': 'Accept-Encoding'
  };
  
  switch (assetType) {
    case 'css':
    case 'js':
      return {
        ...baseHeaders,
        'Cache-Control': `public, max-age=${finalConfig.staticAssetMaxAge}, immutable`,
        'Content-Type': assetType === 'css' ? 'text/css' : 'application/javascript'
      };
      
    case 'image':
      return {
        ...baseHeaders,
        'Cache-Control': `public, max-age=${finalConfig.staticAssetMaxAge}, immutable`,
        'Vary': 'Accept-Encoding, Accept'
      };
      
    case 'font':
      return {
        ...baseHeaders,
        'Cache-Control': `public, max-age=${finalConfig.staticAssetMaxAge}, immutable`,
        'Access-Control-Allow-Origin': '*',
        'Cross-Origin-Resource-Policy': 'cross-origin'
      };
      
    case 'html':
      return {
        ...baseHeaders,
        'Cache-Control': `public, max-age=${finalConfig.htmlMaxAge}, must-revalidate`,
        'X-Frame-Options': 'SAMEORIGIN',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      };
      
    case 'api':
      return {
        'Cache-Control': `public, max-age=${finalConfig.apiMaxAge}, stale-while-revalidate=600`,
        'Content-Type': 'application/json',
        'Vary': 'Accept-Encoding'
      };
      
    default:
      return baseHeaders;
  }
}

/**
 * Preload critical resources
 */
export function generatePreloadLinks(assets: AssetInfo[]): string[] {
  return assets
    .filter(asset => ['css', 'js', 'font'].includes(asset.type))
    .map(asset => {
      const asType = asset.type === 'font' ? 'font' : asset.type === 'css' ? 'style' : 'script';
      const crossorigin = asset.type === 'font' ? ' crossorigin' : '';
      return `<${asset.url}>; rel=preload; as=${asType}${crossorigin}`;
    });
}

/**
 * Generate resource hints for performance
 */
export function generateResourceHints(domains: string[]): string[] {
  return domains.map(domain => `<https://${domain}>; rel=preconnect; crossorigin`);
}

/**
 * Analyze cache performance
 */
export interface CacheAnalysis {
  totalAssets: number;
  cachedAssets: number;
  cacheHitRate: number;
  totalSize: number;
  cachedSize: number;
  recommendations: string[];
}

export function analyzeCachePerformance(assets: AssetInfo[]): CacheAnalysis {
  const totalAssets = assets.length;
  const totalSize = assets.reduce((sum, asset) => sum + (asset.size || 0), 0);
  
  // Simulate cache analysis (in real implementation, this would check actual cache status)
  const cachedAssets = Math.floor(totalAssets * 0.8); // Assume 80% cache hit rate
  const cachedSize = Math.floor(totalSize * 0.8);
  const cacheHitRate = cachedAssets / totalAssets;
  
  const recommendations: string[] = [];
  
  if (cacheHitRate < 0.7) {
    recommendations.push('Consider implementing better cache strategies for static assets');
  }
  
  if (totalSize > 5000000) { // 5MB
    recommendations.push('Consider optimizing asset sizes to reduce bandwidth usage');
  }
  
  const largeAssets = assets.filter(asset => (asset.size || 0) > 500000); // 500KB
  if (largeAssets.length > 0) {
    recommendations.push(`Consider optimizing ${largeAssets.length} large assets (>500KB)`);
  }
  
  return {
    totalAssets,
    cachedAssets,
    cacheHitRate,
    totalSize,
    cachedSize,
    recommendations
  };
}

/**
 * Generate service worker cache strategies
 */
export function generateServiceWorkerCache(assets: AssetInfo[]): string {
  const staticAssets = assets.filter(asset => ['css', 'js', 'image', 'font'].includes(asset.type));
  const dynamicAssets = assets.filter(asset => ['html', 'api'].includes(asset.type));
  
  return `
// Cache strategies for different asset types
const CACHE_NAME = 'cheers-marketplace-v${DEFAULT_CACHE_CONFIG.version}';
const STATIC_CACHE = 'static-v${DEFAULT_CACHE_CONFIG.version}';
const DYNAMIC_CACHE = 'dynamic-v${DEFAULT_CACHE_CONFIG.version}';

// Static assets to cache immediately
const STATIC_ASSETS = [
  ${staticAssets.map(asset => `'${asset.url}'`).join(',\n  ')}
];

// Cache strategies
const cacheStrategies = {
  static: 'CacheFirst',
  dynamic: 'StaleWhileRevalidate',
  api: 'NetworkFirst'
};

// Install event - cache static assets
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => cache.addAll(STATIC_ASSETS))
  );
});

// Fetch event - apply cache strategies
self.addEventListener('fetch', event => {
  const url = new URL(event.request.url);
  
  if (STATIC_ASSETS.includes(url.pathname)) {
    // Cache first for static assets
    event.respondWith(
      caches.match(event.request)
        .then(response => response || fetch(event.request))
    );
  } else if (url.pathname.startsWith('/api/')) {
    // Network first for API calls
    event.respondWith(
      fetch(event.request)
        .then(response => {
          const responseClone = response.clone();
          caches.open(DYNAMIC_CACHE)
            .then(cache => cache.put(event.request, responseClone));
          return response;
        })
        .catch(() => caches.match(event.request))
    );
  } else {
    // Stale while revalidate for HTML pages
    event.respondWith(
      caches.match(event.request)
        .then(response => {
          const fetchPromise = fetch(event.request)
            .then(networkResponse => {
              caches.open(DYNAMIC_CACHE)
                .then(cache => cache.put(event.request, networkResponse.clone()));
              return networkResponse;
            });
          return response || fetchPromise;
        })
    );
  }
});
`;
}

/**
 * Optimize Cloudflare cache settings
 */
export function generateCloudflareRules(assets: AssetInfo[]): string {
  const rules: string[] = [];
  
  // Group assets by type
  const assetsByType = assets.reduce((acc, asset) => {
    if (!acc[asset.type]) acc[asset.type] = [];
    acc[asset.type].push(asset);
    return acc;
  }, {} as Record<string, AssetInfo[]>);
  
  // Generate rules for each asset type
  Object.entries(assetsByType).forEach(([type, typeAssets]) => {
    const headers = generateCacheHeaders(type as AssetInfo['type']);
    const paths = typeAssets.map(asset => asset.url).join('\n');
    
    rules.push(`
# ${type.toUpperCase()} files
${paths}
${Object.entries(headers).map(([key, value]) => `  ${key}: ${value}`).join('\n')}
`);
  });
  
  return rules.join('\n');
}

/**
 * Monitor cache performance in the browser
 */
export function initCacheMonitoring(): void {
  if (typeof window === 'undefined') return;
  
  // Monitor cache API usage
  if ('caches' in window) {
    const originalMatch = caches.match;
    let cacheHits = 0;
    let cacheMisses = 0;
    
    caches.match = function(...args) {
      return originalMatch.apply(this, args).then(response => {
        if (response) {
          cacheHits++;
        } else {
          cacheMisses++;
        }
        return response;
      });
    };
    
    // Report cache statistics periodically
    setInterval(() => {
      const total = cacheHits + cacheMisses;
      if (total > 0) {
        console.log('Cache Performance:', {
          hits: cacheHits,
          misses: cacheMisses,
          hitRate: (cacheHits / total * 100).toFixed(2) + '%'
        });
      }
    }, 30000); // Report every 30 seconds
  }
  
  // Monitor resource loading performance
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (entry.transferSize === 0 && entry.decodedBodySize > 0) {
          console.log('Cache hit detected:', entry.name);
        }
      });
    });
    
    observer.observe({ entryTypes: ['resource'] });
  }
}
