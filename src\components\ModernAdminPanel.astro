---
// Modern Admin Panel Component - Compact integrated design
import { getAllProducts } from '../lib/products.ts';
import AdminHeader from './admin/AdminHeader.astro';
import AdminProductList from './admin/AdminProductList.astro';
import AdminProductForm from './admin/AdminProductForm.astro';
import AdminCategoryManager from './admin/AdminCategoryManager.astro';

// Load products and categories at build time for initial display
const initialProducts = await getAllProducts();

// Load categories from data file
let initialCategories: string[] = [];
try {
  const categoriesModule = await import('../data/categories.json');
  initialCategories = categoriesModule.default || [];
} catch (error) {
  console.warn('Could not load categories.json, using empty array:', error);
  initialCategories = [];
}
---

<div class="admin-container">
  <div id="admin-content" class="admin-content">
    <!-- Compact Integrated Admin Components -->
    <AdminHeader />
    <AdminProductList />
    <AdminProductForm />
    <AdminCategoryManager />
  </div>
</div>

<!-- Load external JavaScript -->
<script src="/scripts/admin-panel.js" is:inline></script>
<script define:vars={{ initialProducts, initialCategories }} is:inline>
  // Initialize the admin panel when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new ModernAdminPanel(initialProducts, initialCategories);
  });
</script>
