---
export const prerender = true;

import Layout from '../../layouts/Layout.astro';
---

<Layout
  title="Admin Login - Cheers Marketplace"
  description="Secure admin login for Cheers Marketplace management panel."
  noIndex={true}
>
  <Fragment slot="head">
    <meta name="robots" content="noindex, nofollow" />
  </Fragment>

  <main class="login-main">
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <div class="login-logo">
            <img src="/logo.png" alt="Cheers Marketplace" class="logo-image" />
          </div>
          <h1>Admin Login</h1>
          <p>Enter your credentials to access the admin panel</p>
        </div>

        <form id="login-form" class="login-form">
          <div class="form-group">
            <label for="password" class="form-label">Password</label>
            <div class="password-input-wrapper">
              <input 
                type="password" 
                id="password" 
                name="password" 
                class="form-input" 
                required 
                autocomplete="current-password"
                placeholder="Enter admin password"
              />
              <button type="button" class="password-toggle" id="password-toggle" aria-label="Toggle password visibility">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="eye-icon">
                  <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                </svg>
              </button>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn-login" id="login-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10,17V14H3V10H10V7L15,12L10,17M10,2H19A2,2 0 0,1 21,4V20A2,2 0 0,1 19,22H10A2,2 0 0,1 8,20V18H10V20H19V4H10V6H8V4A2,2 0 0,1 10,2Z"/>
              </svg>
              Sign In
            </button>
          </div>

          <!-- Status Messages -->
          <div id="login-status" class="login-status" style="display: none;"></div>
        </form>

        <div class="login-footer">
          <p>
            <a href="/" class="back-link">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
              </svg>
              Back to Website
            </a>
          </p>
        </div>
      </div>
    </div>
  </main>
</Layout>

<style>
  .login-main {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--background) 0%, var(--light-background) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .login-container {
    width: 100%;
    max-width: 400px;
  }

  .login-card {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border);
    overflow: hidden;
    position: relative;
  }

  .login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
  }

  .login-header {
    text-align: center;
    padding: 3rem 2.5rem 2rem;
    border-bottom: 1px solid var(--border-light);
  }

  .login-logo {
    margin-bottom: 1.5rem;
  }

  .logo-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
  }

  .login-header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text);
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.025em;
  }

  .login-header p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
  }

  .login-form {
    padding: 2.5rem;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-label {
    display: block;
    font-weight: 600;
    color: var(--text);
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
    letter-spacing: -0.01em;
  }

  .password-input-wrapper {
    position: relative;
  }

  .form-input {
    width: 100%;
    padding: 1rem 3rem 1rem 1.25rem;
    border: 2px solid var(--border);
    border-radius: var(--radius-lg);
    background: var(--light-background);
    color: var(--text);
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
    box-sizing: border-box;
  }

  .form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 4px rgba(146, 64, 14, 0.1);
    background: var(--card-bg);
  }

  .password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius);
    transition: all 0.2s ease;
  }

  .password-toggle:hover {
    color: var(--primary);
    background: var(--background);
  }

  .form-actions {
    margin-top: 2rem;
  }

  .btn-login {
    width: 100%;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    box-shadow: var(--shadow-md);
    letter-spacing: -0.01em;
  }

  .btn-login:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  }

  .btn-login:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
  }

  .btn-login:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  .login-status {
    margin-top: 1.5rem;
    padding: 1rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: 0.9rem;
    text-align: center;
  }

  .login-status.success {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    color: #15803d;
    border: 1px solid #22c55e;
  }

  .login-status.error {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #dc2626;
    border: 1px solid #ef4444;
  }

  .login-status.warning {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #d97706;
    border: 1px solid #f59e0b;
  }

  .login-footer {
    padding: 1.5rem 2.5rem;
    background: var(--background);
    border-top: 1px solid var(--border-light);
    text-align: center;
  }

  .back-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
  }

  .back-link:hover {
    color: var(--primary);
    text-decoration: none;
  }

  /* Animation for loading spinner */
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  /* Responsive Design */
  @media (max-width: 480px) {
    .login-main {
      padding: 1rem;
    }

    .login-header {
      padding: 2rem 1.5rem 1.5rem;
    }

    .login-form {
      padding: 2rem 1.5rem;
    }

    .login-footer {
      padding: 1.5rem;
    }

    .logo-image {
      width: 60px;
      height: 60px;
    }

    .login-header h1 {
      font-size: 1.5rem;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('login-form') as HTMLFormElement;
    const passwordInput = document.getElementById('password') as HTMLInputElement;
    const passwordToggle = document.getElementById('password-toggle') as HTMLButtonElement;
    const loginBtn = document.getElementById('login-btn') as HTMLButtonElement;
    const statusDiv = document.getElementById('login-status') as HTMLDivElement;

    // Password visibility toggle
    if (passwordToggle && passwordInput) {
      passwordToggle.addEventListener('click', function() {
        const isPassword = passwordInput.type === 'password';
        passwordInput.type = isPassword ? 'text' : 'password';
        
        const eyeIcon = passwordToggle.querySelector('.eye-icon');
        if (eyeIcon) {
          eyeIcon.innerHTML = isPassword 
            ? '<path d="M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z"/>'
            : '<path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>';
        }
      });
    }

    // Form submission
    if (form && loginBtn && statusDiv) {
      form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Disable form and show loading state
        loginBtn.disabled = true;
        loginBtn.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="animate-spin">
            <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"/>
          </svg>
          Signing In...
        `;

        try {
          const formData = new FormData(form);
          
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            body: formData
          });

          const result = await response.json();

          if (response.ok && result.success) {
            statusDiv.className = 'login-status success';
            statusDiv.textContent = 'Login successful! Redirecting...';
            statusDiv.style.display = 'block';

            // Store session token in localStorage
            if (result.sessionToken) {
              localStorage.setItem('admin_session', result.sessionToken);
              localStorage.setItem('admin_session_expires', result.expiresAt.toString());
            }

            // Redirect to admin panel after short delay
            setTimeout(() => {
              window.location.href = '/admin/';
            }, 1000);
          } else {
            throw new Error(result.error || 'Login failed');
          }
        } catch (error) {
          let errorMessage = 'Login failed. Please try again.';
          let statusClass = 'error';
          
          if (error.message.includes('Too many')) {
            errorMessage = error.message;
            statusClass = 'warning';
          } else if (error.message.includes('Invalid password')) {
            errorMessage = 'Invalid password. Please check your credentials.';
          }
          
          statusDiv.className = `login-status ${statusClass}`;
          statusDiv.textContent = errorMessage;
          statusDiv.style.display = 'block';
        } finally {
          // Reset login button
          loginBtn.disabled = false;
          loginBtn.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M10,17V14H3V10H10V7L15,12L10,17M10,2H19A2,2 0 0,1 21,4V20A2,2 0 0,1 19,22H10A2,2 0 0,1 8,20V18H10V20H19V4H10V6H8V4A2,2 0 0,1 10,2Z"/>
            </svg>
            Sign In
          `;
        }
      });
    }
  });
</script>
