/* Base styles and typography */
html, body {
  margin: 0;
  padding: 0;
  /* Optimized system font stack for maximum performance and consistency */
  font-family: var(--font-system);
  background: var(--background);
  color: var(--text);
  min-height: 100vh;
  transition: all 0.2s ease;
  line-height: 1.6;
  font-size: 16px;
  /* Enhanced font rendering for better readability */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
}

.container {
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 2rem;
}

/* Typography */
h1, h2, h3 {
  font-family: var(--font-serif);
  color: var(--text);
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

h2 {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

p {
  line-height: 1.7;
  color: var(--text-secondary);
}

/* Links */
a {
  color: var(--primary);
  text-decoration: none;
  transition: all 0.2s ease;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Buttons - Using shared variables */
.cta-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--primary);
  color: white;
  font-weight: var(--btn-font-weight);
  font-size: 0.95rem;
  padding: var(--btn-padding);
  border-radius: var(--btn-border-radius);
  box-shadow: var(--shadow);
  border: none;
  transition: var(--btn-transition);
  cursor: pointer;
  text-decoration: none;
  letter-spacing: -0.01em;
}

.cta-btn:hover {
  background: var(--primary-dark);
  color: white;
  box-shadow: var(--btn-hover-shadow);
  transform: var(--btn-hover-transform);
  text-decoration: none;
}

.cta-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow);
}

/* Main layout */
main {
  padding-top: 2rem;
  min-height: calc(100vh - 72px);
}

/* Footer */
footer {
  margin-top: 4rem;
  color: var(--muted);
  font-size: 0.875rem;
  text-align: center;
  border-top: 1px solid var(--border);
  padding: 2rem 0;
  background: var(--light-background);
}

/* Note: Accessibility, performance, and print styles moved to responsive.css to avoid duplication */
