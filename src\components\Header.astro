---
// Mobile-optimized header with hamburger menu
import CartButton from './CartButton.astro';
---
<header class="site-header">
  <div class="container header-flex">
    <a href="/" class="logo">
      <div class="logo-container">
        <img src="/logo.png" alt="Cheers Marketplace" class="logo-image" width="140" height="140" loading="eager" decoding="sync" fetchpriority="high" />
      </div>
    </a>

    <!-- Mobile hamburger button -->
    <button class="mobile-menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
      <div class="hamburger-icon">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </div>
    </button>

    <!-- Navigation menu -->
    <nav class="main-nav" id="main-nav">
      <a href="/">Home</a>
      <a href="/products/">Products</a>
      <a href="/condition-guide/">Condition Guide</a>
      <a href="/about/">About</a>
      <a href="/faq/">FAQ</a>
      <a href="/contact/">Contact</a>
    </nav>

    <!-- User Actions: Account & Cart -->
    <div class="header-actions">
      <button class="snipcart-customer-signin account-btn" aria-label="My Account">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
          <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
        </svg>
        <span class="account-text">My Account</span>
      </button>
      <CartButton />
    </div>
  </div>
</header>

<!-- Mobile menu functionality -->
<script is:inline>
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');

    if (mobileMenuToggle && mainNav) {
      // Toggle menu on button click
      mobileMenuToggle.addEventListener('click', function(event) {
        event.preventDefault();
        event.stopPropagation();

        const isOpen = mainNav.classList.contains('nav-open');

        if (isOpen) {
          mainNav.classList.remove('nav-open');
          mobileMenuToggle.classList.remove('menu-open');
          mobileMenuToggle.setAttribute('aria-expanded', 'false');
        } else {
          mainNav.classList.add('nav-open');
          mobileMenuToggle.classList.add('menu-open');
          mobileMenuToggle.setAttribute('aria-expanded', 'true');
        }
      });

      // Close menu when clicking outside
      document.addEventListener('click', function(event) {
        if (!mobileMenuToggle.contains(event.target) && !mainNav.contains(event.target)) {
          if (mainNav.classList.contains('nav-open')) {
            mainNav.classList.remove('nav-open');
            mobileMenuToggle.classList.remove('menu-open');
            mobileMenuToggle.setAttribute('aria-expanded', 'false');
          }
        }
      });

      // Close menu when clicking on a nav link
      const navLinks = mainNav.querySelectorAll('a');
      navLinks.forEach(link => {
        link.addEventListener('click', function() {
          mainNav.classList.remove('nav-open');
          mobileMenuToggle.classList.remove('menu-open');
          mobileMenuToggle.setAttribute('aria-expanded', 'false');
        });
      });
    }
  });
</script>

<style>
  /* Logo styling - Compact professional design */
  .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: opacity 0.2s ease;
    min-height: 60px; /* Compact header height */
    flex-shrink: 0; /* Prevent logo from shrinking */
  }

  .logo-container {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    overflow: hidden; /* Hide any alt text overflow */
  }

  .logo-image {
    height: 60px;
    width: 60px; /* Compact professional size */
    max-width: 60px;
    transition: opacity 0.2s ease;
    display: block; /* Ensure no inline spacing */
    object-fit: contain; /* Maintain aspect ratio */
    background: transparent; /* Prevent alt text background */
    color: transparent; /* Hide alt text */
    font-size: 0; /* Hide alt text completely */
  }

  /* Show image when loaded, hide alt text */
  .logo-image[src] {
    color: transparent;
  }

  .logo:hover .logo-image {
    opacity: 0.8;
  }

  /* Responsive logo sizing - Compact professional design */
  @media (max-width: 768px) {
    .logo {
      min-height: 50px;
    }

    .logo-container {
      width: 50px;
      height: 50px;
    }

    .logo-image {
      height: 50px;
      width: 50px;
      max-width: 50px;
    }
  }

  @media (max-width: 480px) {
    .logo {
      min-height: 45px;
    }

    .logo-container {
      width: 45px;
      height: 45px;
    }

    .logo-image {
      height: 45px;
      width: 45px;
      max-width: 45px;
    }
  }

  /* Header actions positioning */
  .header-actions {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  /* Account button styling */
  .account-btn {
    background: none;
    border: 1px solid var(--border, #e5e7eb);
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text, #374151);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
  }

  .account-btn:hover {
    background: var(--light-background, #f9fafb);
    border-color: var(--primary, #3b82f6);
    color: var(--primary, #3b82f6);
  }

  .account-btn svg {
    flex-shrink: 0;
  }

  /* Ensure header flex layout accommodates actions */
  .header-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
  }

  /* Header needs to be positioned relative for mobile menu positioning */
  .site-header {
    position: relative;
  }

  /* Mobile hamburger menu - High specificity to override other styles */
  .site-header .mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background: transparent;
    border: 1px solid #ddd;
    cursor: pointer;
    padding: 10px;
    margin-left: auto;
    z-index: 1001;
    border-radius: 6px;
    position: relative;
  }

  .site-header .mobile-menu-toggle:hover {
    background: #f8f9fa;
    border-color: #999;
  }

  .site-header .mobile-menu-toggle:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
  }

  .site-header .mobile-menu-toggle .hamburger-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 22px;
    height: 18px;
  }

  .site-header .mobile-menu-toggle .hamburger-line {
    width: 22px !important;
    height: 3px !important;
    background-color: #92400e !important;
    background: #92400e !important;
    margin: 2px 0 !important;
    transition: all 0.3s ease;
    border-radius: 2px;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
    z-index: 1 !important;
  }

  .site-header .mobile-menu-toggle.menu-open .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .site-header .mobile-menu-toggle.menu-open .hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .site-header .mobile-menu-toggle.menu-open .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  /* Desktop navigation styles */
  @media (min-width: 769px) {
    .site-header .main-nav {
      display: flex !important;
      gap: 3rem;
      align-items: center;
      flex: 1;
      justify-content: center;
      margin: 0 2rem;
    }
  }

  /* Mobile adjustments and hamburger menu display */
  @media (max-width: 768px) {
    .header-flex {
      padding: 0 1rem;
      min-height: 56px;
      justify-content: space-between;
      align-items: center;
      gap: 1rem;
    }

    .logo {
      flex-shrink: 0;
      order: 1;
      min-height: 50px; /* Compact mobile design */
    }

    .logo-container {
      width: 50px;
      height: 50px;
    }

    .logo-image {
      width: 50px;
      height: 50px;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      order: 2;
      flex: 1;
      justify-content: center;
      margin: 0 1rem;
    }

    .account-btn .account-text {
      display: none;
    }

    .account-btn {
      padding: 0.5rem;
      min-width: 44px;
      min-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .site-header .mobile-menu-toggle {
      display: flex !important;
      order: 3;
      flex-shrink: 0;
    }

  /* Mobile navigation - Override all other styles with highest specificity */
  .site-header .main-nav {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    background: #ffffff !important;
    border-top: 1px solid #e2e8f0 !important;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1) !important;
    flex-direction: column !important;
    gap: 0 !important;
    margin: 0 !important;
    padding: 1rem 0 !important;
    z-index: 1000 !important;
    order: 4;
    /* Use visibility and opacity for better performance */
    display: flex !important;
    visibility: hidden !important;
    opacity: 0 !important;
    transform: translateY(-10px) !important;
    transition: all 0.2s ease !important;
    pointer-events: none !important;
  }

  .site-header .main-nav.nav-open {
    visibility: visible !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
  }

  .site-header .main-nav a {
    padding: 1rem 1.5rem !important;
    font-size: 1rem !important;
    border-radius: 0 !important;
    border-bottom: 1px solid var(--border-light, #f1f5f9) !important;
  }

  .site-header .main-nav a:last-child {
    border-bottom: none !important;
  }
}

@media (max-width: 480px) {
  .header-flex {
    padding: 0 0.75rem;
    min-height: 52px;
    gap: 0.75rem;
  }

  .logo {
    min-height: 45px; /* Compact mobile design */
  }

  .logo-container {
    width: 45px;
    height: 45px;
  }

  .logo-image {
    width: 45px;
    height: 45px;
  }

  .header-actions {
    gap: 0.5rem;
    margin: 0 0.5rem;
  }

  .account-btn {
    min-width: 40px;
    min-height: 40px;
    padding: 0.375rem;
  }

  .site-header .mobile-menu-toggle {
    width: 36px;
    height: 36px;
  }

  .site-header .mobile-menu-toggle .hamburger-icon {
    width: 18px;
    height: 14px;
  }

  .site-header .mobile-menu-toggle .hamburger-line {
    width: 18px;
  }
}
</style>
