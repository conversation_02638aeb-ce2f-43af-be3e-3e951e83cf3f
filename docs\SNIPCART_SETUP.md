# Snipcart Integration Setup Guide

## Overview
Your Cheers Marketplace now uses <PERSON><PERSON><PERSON><PERSON>t for professional e-commerce functionality including cart management, checkout, payment processing, and order management.

## 🔑 API Key Configuration

### 1. Environment Variables
Create a `.env` file in your project root with your Snipcart API key:

```bash
# Copy from .env.example and update with your keys
PUBLIC_SNIPCART_API_KEY=YTY0ZTIxNjYtNDVlMC00NmUyLWFkNjItYTg3ZmNhMTc4MDQyNjM4ODczNzI4NTM0MTY0NDA4
SNIPCART_WEBHOOK_SECRET=your_webhook_secret_here
```

### 2. Production vs Test Keys
- **Test Key** (current): `YTY0ZTIxNjYtNDVlMC00NmUyLWFkNjItYTg3ZmNhMTc4MDQyNjM4ODczNzI4NTM0MTY0NDA4`
- **Live Key**: Get from [Snipcart Dashboard](https://app.snipcart.com/dashboard/account/credentials) when ready for production

## 🔗 Webhook Configuration

### 1. Set Up Webhook URL
In your [Snipcart Dashboard → Webhooks](https://app.snipcart.com/dashboard/webhooks), add:

```
https://your-cloudflare-pages-domain.pages.dev/api/webhooks/snipcart
```

**For Cloudflare Pages:**
- Use your `.pages.dev` domain for testing
- Use your custom domain for production
- Webhooks work automatically with Cloudflare Pages Functions

### 2. Webhook Events
Your webhook endpoint handles these events:
- `order.completed` - New order placed
- `order.status.changed` - Order status updated
- `order.paymentStatus.changed` - Payment status updated
- `order.trackingNumber.changed` - Tracking info added
- `order.refund.created` - Refund processed
- `order.notification.created` - Order notification added

### 3. Webhook Security
- Uses `X-Snipcart-RequestToken` header for verification
- Validates tokens against Snipcart's API
- Logs all events for debugging

## 🛍️ Product Configuration

### 1. Product Validation Endpoint
Snipcart validates products via: `/api/snipcart/products/{product-id}`

**Cloudflare Pages Integration:**
- Products are automatically synced when you add them via admin panel
- Snipcart validates products on-demand when customers add to cart
- No manual product upload to Snipcart required

### 2. Product Data Structure
Each product includes:
- Basic info (name, price, description, image)
- Custom fields (Condition: "Gently Used", Category)
- Metadata (key points, defects, timestamps)
- Inventory settings (max quantity: 1 for used goods)
- Cheers Marketplace disclaimers

### 3. Admin Panel Integration
When you add/edit products via your admin panel:
- Products are saved to GitHub (triggers rebuild)
- Snipcart automatically validates new products when customers try to purchase
- No additional steps needed for Snipcart integration

## 🎨 Cart Features

### 1. Cart Button
- Professional cart icon in header
- Shows item count badge
- Side modal cart (not full page)
- Stays on page when adding items

### 2. Product Pages
- "Add to Cart" buttons on all product pages
- Price displayed on button
- Contact option still available
- Share functionality maintained

### 3. Product Listings
- "Add to Cart" buttons on product cards
- Hover effects and professional styling
- Responsive design for all devices

## 🔧 Dashboard Configuration

### 1. Store Settings
In [Snipcart Dashboard → Store Configuration](https://app.snipcart.com/dashboard/settings):

**General Settings:**
- Store Name: "Cheers Marketplace"
- Currency: USD
- Country: United States
- Timezone: Pacific Time

**Shipping:**
- Configure shipping rates for your area
- Consider local pickup options
- Set up shipping zones

**Taxes:**
- Configure for California (8.75% sales tax)
- Set up tax rules for your jurisdiction

**Payment Methods:**
- Enable credit/debit cards
- Consider PayPal for customer convenience
- Set up your payment gateway

### 2. Domain Configuration
In [Domains and URLs](https://app.snipcart.com/dashboard/domains):
- Add your production domain
- Add your development domain for testing

## 📧 Email Templates

### 1. Order Confirmation
Customize email templates in [Email Templates](https://app.snipcart.com/dashboard/email-templates):
- Order confirmation
- Shipping notifications
- Refund confirmations

### 2. Branding
- Add your logo
- Use your brand colors
- Include family business messaging

## 🧪 Testing

### 1. Test Mode
- Use test API key for development
- Test orders won't charge real money
- Use test credit card: 4242 4242 4242 4242

### 2. Test Scenarios
- Add products to cart
- Complete checkout process
- Test webhook notifications
- Verify order appears in dashboard

## 🚀 Going Live

### 1. Switch to Live Mode
1. Get live API key from Snipcart dashboard
2. Update environment variables
3. Update webhook URLs to production domain
4. Test with small real transaction

### 2. Production Checklist
- [ ] Live API key configured
- [ ] Webhook URL updated to production
- [ ] Domain added to Snipcart dashboard
- [ ] Payment gateway configured
- [ ] Shipping rates set up
- [ ] Tax settings configured
- [ ] Email templates customized
- [ ] SSL certificate active
- [ ] Test transaction completed

## 🔍 Monitoring

### 1. Order Management
- Monitor orders in [Snipcart Dashboard](https://app.snipcart.com/dashboard/orders)
- Update order status as you fulfill them
- Add tracking numbers when shipping

### 2. Webhook Logs
- Check webhook logs in dashboard
- Monitor your server logs for webhook events
- Debug any failed webhook calls

## 💡 Tips for Used Goods Business

### 1. Inventory Management
- Each item is unique (max quantity: 1)
- Mark items as sold via order webhooks
- Consider removing sold items from your site

### 2. Customer Communication
- Use order notifications for updates
- Add tracking numbers when shipping
- Respond to customer inquiries promptly

### 3. Quality Assurance
- Maintain detailed product descriptions
- Include defect information clearly
- Use multiple product photos

## 🆘 Support

### 1. Snipcart Support
- [Documentation](https://docs.snipcart.com/v3/)
- [Support Portal](https://snipcart.com/support)
- [Community Forum](https://snipcart.com/community)

### 2. Debugging
- Check browser console for errors
- Monitor webhook endpoint logs
- Use Snipcart dashboard webhook details
- Test with Snipcart's webhook testing tool

## 📋 Next Steps

1. **Test the integration** with your test API key
2. **Configure webhook URL** in Snipcart dashboard  
3. **Set up shipping and tax settings**
4. **Customize email templates**
5. **Test complete order flow**
6. **Go live** when ready!
