---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import '../assets/global.css';

const pageTitle = "Page Not Found | Cheers Marketplace";
const pageDescription = "The page you're looking for doesn't exist. Browse our quality secondhand goods or return to the homepage.";
---

<Layout 
  title={pageTitle}
  description={pageDescription}
  noIndex={true}
>
  <Fragment slot="head">
    <!-- Additional meta tags for 404 page -->
    <meta name="robots" content="noindex, nofollow" />
    <!-- Canonical URL handled by Layout.astro -->
  </Fragment>

  <Header />

  <main class="error-main">
    <section class="error-section">
      <div class="container">
        <div class="error-content">
          <!-- Error Icon -->
          <div class="error-icon">
            <svg width="120" height="120" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <circle cx="12" cy="12" r="10"/>
              <path d="m9 9 6 6"/>
              <path d="m15 9-6 6"/>
            </svg>
          </div>

          <!-- Error Message -->
          <div class="error-message">
            <h1>Page Not Found</h1>
            <p class="error-description">
              Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
            </p>
          </div>

          <!-- Action Buttons -->
          <div class="error-actions">
            <a href="/" class="btn-primary">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
              </svg>
              Back to Home
            </a>
            <a href="/products" class="btn-secondary">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect width="7" height="7" x="3" y="3" rx="1"/>
                <rect width="7" height="7" x="14" y="3" rx="1"/>
                <rect width="7" height="7" x="3" y="14" rx="1"/>
                <rect width="7" height="7" x="14" y="14" rx="1"/>
              </svg>
              Browse Products
            </a>
          </div>

          <!-- Helpful Links -->
          <div class="helpful-links">
            <h2>Looking for something specific?</h2>
            <div class="links-grid">
              <a href="/products" class="help-link">
                <div class="help-icon">🛍️</div>
                <div class="help-content">
                  <h3>All Products</h3>
                  <p>Browse our complete collection</p>
                </div>
              </a>
              <a href="/about" class="help-link">
                <div class="help-icon">ℹ️</div>
                <div class="help-content">
                  <h3>About Us</h3>
                  <p>Learn about our family business</p>
                </div>
              </a>
              <a href="/faq" class="help-link">
                <div class="help-icon">❓</div>
                <div class="help-content">
                  <h3>FAQ</h3>
                  <p>Common questions and answers</p>
                </div>
              </a>
              <a href="mailto:<EMAIL>" class="help-link">
                <div class="help-icon">📧</div>
                <div class="help-content">
                  <h3>Contact Us</h3>
                  <p>Get in touch for help</p>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <Footer />
</Layout>

<style>
  /* Fix layout centering for 404 page */
  .error-main {
    padding-top: 0;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .error-section {
    padding: 4rem 0;
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .container {
    max-width: var(--container-width);
    margin: 0 auto !important;
    padding: 0 2rem;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .error-content {
    max-width: 600px;
    margin: 0 auto !important;
    width: 100%;
  }

  .error-icon {
    color: var(--primary);
    margin-bottom: 2rem;
    opacity: 0.8;
  }

  .error-message h1 {
    font-family: Georgia, 'Times New Roman', Times, serif;
    font-size: 3rem;
    font-weight: 700;
    color: var(--text);
    margin: 0 0 1rem 0;
    letter-spacing: -0.025em;
  }

  .error-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin: 0 0 3rem 0;
  }

  .error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 4rem;
  }

  .btn-primary,
  .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
  }

  .btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    box-shadow: var(--shadow);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .btn-secondary {
    background: var(--light-background);
    color: var(--text-secondary);
    border: 1px solid var(--border);
  }

  .btn-secondary:hover {
    background: var(--border-light);
    color: var(--text);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }

  .helpful-links {
    border-top: 1px solid var(--border-light);
    padding-top: 3rem;
  }

  .helpful-links h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text);
    margin: 0 0 2rem 0;
    font-family: Georgia, 'Times New Roman', Times, serif;
  }

  .links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
  }

  .help-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--light-background);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all 0.2s ease;
  }

  .help-link:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-sm);
    transform: translateY(-2px);
  }

  .help-icon {
    font-size: 2rem;
    flex-shrink: 0;
  }

  .help-content {
    text-align: left;
  }

  .help-content h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text);
    margin: 0 0 0.25rem 0;
  }

  .help-content p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .error-section {
      padding: 2rem 0;
    }

    .error-message h1 {
      font-size: 2.5rem;
    }

    .error-description {
      font-size: 1.125rem;
    }

    .error-actions {
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }

    .btn-primary,
    .btn-secondary {
      width: 100%;
      max-width: 300px;
      justify-content: center;
    }

    .links-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .help-link {
      padding: 1.25rem;
    }
  }

  @media (max-width: 480px) {
    .error-icon svg {
      width: 80px;
      height: 80px;
    }

    .error-message h1 {
      font-size: 2rem;
    }

    .error-description {
      font-size: 1rem;
    }

    .helpful-links h2 {
      font-size: 1.25rem;
    }
  }
</style>
