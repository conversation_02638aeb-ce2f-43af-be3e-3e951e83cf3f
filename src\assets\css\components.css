/* UI Components - Modals, Buttons, Forms */

/* Enhanced Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  backdrop-filter: blur(4px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--light-background);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  transform: scale(0.95) translateY(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border);
}

.modal-overlay.active .modal-content {
  transform: scale(1) translateY(0);
}

.modal-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--border-light);
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0.25rem;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modal-close:hover {
  background: var(--border);
  color: var(--text);
}

.modal-body {
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  background: var(--border-light);
}

/* Button Styles - Using shared variables */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: var(--btn-padding);
  border: none;
  border-radius: var(--btn-border-radius);
  font-size: var(--btn-font-size);
  font-weight: var(--btn-font-weight);
  cursor: pointer;
  transition: var(--btn-transition);
  text-decoration: none;
  line-height: 1;
  white-space: nowrap;
  user-select: none;
}

.btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.btn.primary {
  background: var(--primary);
  color: white;
}

.btn.primary:hover {
  background: var(--primary-dark);
  transform: var(--btn-hover-transform);
  box-shadow: var(--btn-hover-shadow);
}

.btn.secondary {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
}

.btn.secondary:hover {
  background: var(--border-light);
  border-color: var(--border);
}

.btn.outline {
  background: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.btn.outline:hover {
  background: var(--primary);
  color: white;
}

.btn.danger {
  background: var(--danger);
  color: white;
}

.btn.danger:hover {
  background: var(--danger-dark);
  transform: var(--btn-hover-transform);
  box-shadow: var(--btn-hover-shadow);
}

.btn.success {
  background: var(--success);
  color: white;
}

.btn.success:hover {
  background: var(--success-dark);
  transform: var(--btn-hover-transform);
  box-shadow: var(--btn-hover-shadow);
}

.btn.warning {
  background: var(--warning);
  color: white;
}

.btn.warning:hover {
  background: var(--warning-dark);
  transform: var(--btn-hover-transform);
  box-shadow: var(--btn-hover-shadow);
}

.btn.small {
  padding: var(--btn-padding-sm);
  font-size: var(--btn-font-size-sm);
}

.btn.large {
  padding: var(--btn-padding-lg);
  font-size: var(--btn-font-size-lg);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text);
  font-size: 0.875rem;
}

.form-label.required::after {
  content: " *";
  color: #dc2626;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--light-background);
  color: var(--text);
  transition: all 0.2s ease;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: #dc2626;
}

.form-input.error:focus,
.form-textarea.error:focus,
.form-select.error:focus {
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.form-help {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.form-error {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #dc2626;
}

.form-checkbox,
.form-radio {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.form-checkbox input,
.form-radio input {
  width: auto;
  margin: 0;
}

/* Alert/Notification Styles */
.alert {
  padding: 1rem 1.5rem;
  border-radius: var(--radius);
  margin-bottom: 1.5rem;
  border: 1px solid;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.alert-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-top: 0.125rem;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
}

.alert-message {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.alert.success {
  background: #f0fdf4;
  border-color: #22c55e;
  color: #15803d;
}

.alert.error {
  background: #fef2f2;
  border-color: #ef4444;
  color: #dc2626;
}

.alert.warning {
  background: #fffbeb;
  border-color: #f59e0b;
  color: #d97706;
}

.alert.info {
  background: #fef3c7;
  border-color: #d97706;
  color: #92400e;
}

/* Loading Spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner.large {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

/* Badge Styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: 1;
}

.badge.primary {
  background: var(--primary);
  color: white;
}

.badge.secondary {
  background: var(--border-light);
  color: var(--text-secondary);
  border: 1px solid var(--border);
}

.badge.success {
  background: #f0fdf4;
  color: #15803d;
  border: 1px solid #22c55e;
}

.badge.danger {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #ef4444;
}

.badge.warning {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #f59e0b;
}

.badge.info {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #d97706;
}

/* Tooltip */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--text);
  color: var(--light-background);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
}

.tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
}

.tooltip-content::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--text);
}
